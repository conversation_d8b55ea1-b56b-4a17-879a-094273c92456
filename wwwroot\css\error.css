/* ==========================================================================
   ERROR PAGE STYLES
   ========================================================================== */

/* ==========================================================================
   ERROR PAGE LAYOUT
   ========================================================================== */

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
    padding: var(--space-8);
    max-width: 800px;
    margin: 0 auto;
}

/* ==========================================================================
   ERROR CARD
   ========================================================================== */

.error-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    padding: var(--space-12);
    width: 100%;
    max-width: 600px;
    position: relative;
    overflow: hidden;
}

.error-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--error), #dc2626, var(--error));
}

/* ==========================================================================
   ERROR ICON
   ========================================================================== */

.error-icon {
    width: 5rem;
    height: 5rem;
    background: linear-gradient(135deg, var(--error), #dc2626);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6) auto;
    box-shadow: var(--shadow-lg);
    position: relative;
}

.error-icon::before {
    content: '⚠';
    font-size: var(--text-3xl);
    color: var(--text-inverse);
    font-weight: var(--font-bold);
}

.error-icon::after {
    content: '';
    position: absolute;
    width: 6rem;
    height: 6rem;
    border: 2px solid var(--error);
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

/* ==========================================================================
   ERROR CONTENT
   ========================================================================== */

.error-title {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin: 0 0 var(--space-4) 0;
    background: linear-gradient(135deg, var(--text-primary), var(--gray-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.error-subtitle {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    margin: 0 0 var(--space-6) 0;
    line-height: 1.5;
}

.error-description {
    font-size: var(--text-base);
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0 0 var(--space-8) 0;
}

/* ==========================================================================
   ERROR DETAILS
   ========================================================================== */

.error-details {
    background: var(--gray-50);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin: var(--space-6) 0;
    text-align: left;
}

.error-details h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-4) 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.error-details h3::before {
    content: '🔍';
    font-size: var(--text-base);
}

.error-details p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--space-3) 0;
    line-height: 1.5;
}

.error-details strong {
    color: var(--text-primary);
    font-weight: var(--font-semibold);
}

.error-details code {
    background: var(--gray-100);
    color: var(--primary-700);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-family: 'Courier New', monospace;
    font-size: var(--text-sm);
}

/* ==========================================================================
   DEVELOPMENT INFO
   ========================================================================== */

.development-info {
    background: linear-gradient(135deg, var(--warning), #f59e0b);
    color: var(--text-inverse);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin: var(--space-6) 0;
    text-align: left;
    position: relative;
    overflow: hidden;
}

.development-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    opacity: 0.1;
    z-index: 0;
}

.development-info > * {
    position: relative;
    z-index: 1;
}

.development-info h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    margin: 0 0 var(--space-4) 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.development-info h3::before {
    content: '⚠️';
    font-size: var(--text-base);
}

.development-info p {
    font-size: var(--text-sm);
    margin: 0 0 var(--space-3) 0;
    line-height: 1.5;
    opacity: 0.95;
}

.development-info p:last-child {
    margin-bottom: 0;
}

.development-info strong {
    font-weight: var(--font-bold);
    text-decoration: underline;
}

/* ==========================================================================
   ERROR ACTIONS
   ========================================================================== */

.error-actions {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    align-items: center;
    margin-top: var(--space-8);
}

.error-actions .btn-group {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
    justify-content: center;
}

.btn-home {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    box-shadow: var(--shadow-md);
}

.btn-home:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-inverse);
}

.btn-back {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.btn-back:hover {
    background: var(--gray-50);
    color: var(--text-primary);
    border-color: var(--border-dark);
    transform: translateY(-1px);
}

.error-help {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    margin-top: var(--space-4);
    text-align: center;
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */

@media (max-width: 768px) {
    .error-container {
        padding: var(--space-4);
        min-height: 50vh;
    }
    
    .error-card {
        padding: var(--space-8);
    }
    
    .error-title {
        font-size: var(--text-3xl);
    }
    
    .error-subtitle {
        font-size: var(--text-lg);
    }
    
    .error-actions .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-home,
    .btn-back {
        width: 100%;
        justify-content: center;
    }
}

/* ==========================================================================
   PRINT STYLES
   ========================================================================== */

@media print {
    .error-card::before,
    .error-icon::after,
    .development-info::before {
        display: none;
    }
    
    .error-actions {
        display: none;
    }
    
    .development-info {
        background: #f3f4f6;
        color: var(--text-primary);
    }
}
