/* ==========================================================================
   COMPONENT STYLES - Reusable UI Components
   ========================================================================== */

/* ==========================================================================
   BUTTONS
   ========================================================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    line-height: 1;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Variants */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-medium);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--border-dark);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-600);
    border-color: var(--primary-600);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-50);
    color: var(--primary-700);
    border-color: var(--primary-700);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: none;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--gray-100);
    color: var(--text-primary);
}

.btn-danger {
    background: linear-gradient(135deg, var(--error), #dc2626);
    color: var(--text-inverse);
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-base);
}

.btn-xl {
    padding: var(--space-5) var(--space-8);
    font-size: var(--text-lg);
}

/* ==========================================================================
   FORM ELEMENTS
   ========================================================================== */

.form-group {
    margin-bottom: var(--space-6);
}

.form-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.form-label .required {
    color: var(--error);
    margin-left: var(--space-1);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-primary);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
    transform: translateY(-1px);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
    background: var(--gray-100);
    color: var(--text-tertiary);
    cursor: not-allowed;
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--text-tertiary);
}

/* Enhanced Select Styling */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-3) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: var(--space-10);
    cursor: pointer;
}

.form-select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23a855f7' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Form Help Text */
.form-help {
    margin-top: var(--space-2);
    font-size: var(--text-xs);
    color: var(--text-tertiary);
}

/* Form Error States */
.form-input.error,
.form-select.error,
.form-textarea.error {
    border-color: var(--error);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
    margin-top: var(--space-2);
    font-size: var(--text-xs);
    color: var(--error);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* ==========================================================================
   CARDS
   ========================================================================== */

.card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-light);
    background: linear-gradient(135deg, var(--bg-primary), var(--gray-50));
}

.card-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.card-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: var(--space-1) 0 0 0;
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--border-light);
    background: var(--gray-50);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--space-3);
}

/* Glass Card Variant */
.card-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
}

/* ==========================================================================
   TABLES
   ========================================================================== */

.table-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--text-sm);
}

.table th {
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
    padding: var(--space-4) var(--space-6);
    text-align: left;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-medium);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    color: var(--text-secondary);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: var(--primary-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Sticky Action Column */
.table th.actions,
.table td.actions {
    position: sticky;
    right: 0;
    background: var(--bg-primary);
    border-left: 1px solid var(--border-light);
    z-index: var(--z-sticky);
}

.table th.actions {
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
}

.table tbody tr:hover td.actions {
    background: var(--primary-50);
}

/* ==========================================================================
   MODALS
   ========================================================================== */

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: var(--z-modal-backdrop);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.modal-backdrop.show {
    opacity: 1;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
}

.modal-dialog {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.95);
    transition: transform var(--transition-normal);
}

.modal.show .modal-dialog {
    transform: scale(1);
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--text-xl);
    color: var(--text-tertiary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--space-6);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--border-light);
    background: var(--gray-50);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--space-3);
}

/* ==========================================================================
   ALERTS & NOTIFICATIONS
   ========================================================================== */

.alert {
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid;
    margin-bottom: var(--space-4);
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    position: relative;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.alert:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.1), 0 3px 6px -1px rgba(0, 0, 0, 0.06);
}

.alert::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.alert-success {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-success::before {
    background: #22c55e;
}

.alert-warning {
    background: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

.alert-warning::before {
    background: #f59e0b;
}

.alert-error {
    background: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-error::before {
    background: #ef4444;
}

.alert-info {
    background: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}

.alert-info::before {
    background: #3b82f6;
}

.alert-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    margin-top: 2px;
}

.alert-content {
    flex-grow: 1;
    font-size: 0.9375rem;
    line-height: 1.5;
}

.alert-close {
    flex-shrink: 0;
    background: transparent;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: inherit;
    opacity: 0.6;
    transition: opacity 0.2s ease;
    border-radius: var(--radius-md);
}

.alert-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.05);
}

@keyframes slideIn {
    from {
        transform: translateY(-10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Toast Container */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1050;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.toast {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    min-width: 300px;
    max-width: 400px;
    animation: slideIn 0.3s ease-out;
    border: none;
}

.toast-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toast-body {
    padding: 1rem;
    font-size: 0.9375rem;
    line-height: 1.5;
}

.toast-success {
    border-left: 4px solid #22c55e;
}

.toast-warning {
    border-left: 4px solid #f59e0b;
}

.toast-error {
    border-left: 4px solid #ef4444;
}

.toast-info {
    border-left: 4px solid #3b82f6;
}

/* Custom Validation Toast */
.validation-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
    min-width: 300px;
    max-width: 400px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease-out;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.validation-toast.show {
    opacity: 1;
    transform: translateX(0);
}

.validation-toast-error {
    border-left: 4px solid var(--error);
}

.validation-toast-content {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    gap: 0.75rem;
}

.validation-toast-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.validation-toast-message {
    flex: 1;
    font-size: 0.9375rem;
    line-height: 1.5;
    color: var(--text-primary);
    font-weight: var(--font-medium);
}

.validation-toast-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    line-height: 1;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.validation-toast-close:hover {
    background: rgba(0, 0, 0, 0.05);
    color: var(--text-primary);
}

/* ==========================================================================
   BADGES & PILLS
   ========================================================================== */

.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    border-radius: var(--radius-full);
    white-space: nowrap;
}

.badge-primary {
    background: var(--primary-100);
    color: var(--primary-800);
}

.badge-secondary {
    background: var(--gray-100);
    color: var(--gray-800);
}

.badge-success {
    background: #dcfce7;
    color: #166534;
}

.badge-warning {
    background: #fef3c7;
    color: #92400e;
}

.badge-error {
    background: #fee2e2;
    color: #991b1b;
}

/* ==========================================================================
   LOADING STATES
   ========================================================================== */

.spinner {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: inline-flex;
    gap: var(--space-1);
}

.loading-dots span {
    width: 0.5rem;
    height: 0.5rem;
    background: var(--primary-600);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* ==========================================================================
   TABS
   ========================================================================== */

.tabs {
    border-bottom: 1px solid var(--border-light);
}

.tabs-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--space-1);
}

.tabs-item {
    margin: 0;
}

.tabs-button {
    display: block;
    padding: var(--space-3) var(--space-4);
    border: none;
    background: none;
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.tabs-button:hover {
    color: var(--text-primary);
    background: var(--gray-50);
}

.tabs-button[aria-selected="true"] {
    color: var(--primary-600);
    background: var(--bg-primary);
    border-bottom: 2px solid var(--primary-600);
}

.tabs-panel {
    padding: var(--space-6);
    background: var(--bg-primary);
}

/* ==========================================================================
   SELECT2 ENHANCED DROPDOWN STYLING
   ========================================================================== */

/* Select2 Container */
.select2-container {
    width: 100% !important;
}

.select2-container--default .select2-selection--single {
    height: auto !important;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-medium) !important;
    border-radius: var(--radius-lg) !important;
    font-size: var(--text-sm);
    background: var(--bg-primary) !important;
    color: var(--text-primary);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    min-height: 2.75rem;
    display: flex;
    align-items: center;
}

.select2-container--default .select2-selection--single:focus,
.select2-container--default.select2-container--focus .select2-selection--single {
    outline: none !important;
    border-color: var(--primary-500) !important;
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1) !important;
    transform: translateY(-1px);
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: var(--text-primary) !important;
    line-height: 1.5;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    height: 100%;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: var(--text-tertiary) !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 100% !important;
    right: var(--space-3) !important;
    top: 0 !important;
    width: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: var(--text-secondary) transparent transparent transparent !important;
    border-style: solid;
    border-width: 5px 5px 0 5px;
    height: 0;
    left: 0;
    margin: 0;
    position: static;
    top: 0;
    width: 0;
    transition: all var(--transition-fast);
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent var(--primary-500) transparent !important;
    border-width: 0 5px 5px 5px;
}

/* Select2 Clear Button */
.select2-container--default .select2-selection--single .select2-selection__clear {
    color: var(--text-secondary) !important;
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-right: var(--space-6);
    padding: var(--space-1);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    background: var(--gray-100);
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xs);
}

.select2-container--default .select2-selection--single .select2-selection__clear:hover {
    color: var(--error) !important;
    background: var(--gray-200);
    transform: scale(1.1);
}

/* Select2 Dropdown */
.select2-dropdown {
    border: 1px solid var(--border-light) !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-lg) !important;
    background: var(--bg-primary) !important;
    margin-top: var(--space-1);
    overflow: hidden;
    z-index: var(--z-dropdown) !important;
}

.select2-dropdown--below {
    border-top: none !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
    margin-top: 0;
}

.select2-dropdown--above {
    border-bottom: none !important;
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    margin-bottom: 0;
}

/* Select2 Results */
.select2-results {
    background: var(--bg-primary) !important;
    max-height: 250px !important;
}

.select2-results__options {
    max-height: 200px !important;
    overflow-y: auto !important;
    padding: var(--space-1) 0 !important;
}

.select2-results__option {
    padding: var(--space-3) var(--space-4) !important;
    color: var(--text-secondary) !important;
    cursor: pointer !important;
    transition: all var(--transition-fast) !important;
    font-size: var(--text-sm) !important;
    line-height: 1.5;
    margin: 0 var(--space-2);
    border-radius: var(--radius-md);
}

.select2-results__option:hover,
.select2-results__option--highlighted {
    background: var(--primary-50) !important;
    color: var(--primary-600) !important;
    transform: translateX(2px);
}

.select2-results__option[aria-selected="true"] {
    background: var(--primary-100) !important;
    color: var(--primary-700) !important;
    font-weight: var(--font-medium);
}

.select2-results__option[aria-selected="true"]:hover {
    background: var(--primary-200) !important;
}

/* Select2 No Results */
.select2-results__option--nodata {
    color: var(--text-tertiary) !important;
    text-align: center !important;
    font-style: italic;
    padding: var(--space-6) var(--space-4) !important;
}

/* Custom Scrollbar for Select2 */
.select2-results__options::-webkit-scrollbar {
    width: 4px;
}

.select2-results__options::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

.select2-results__options::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

.select2-results__options::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* ==========================================================================
   DROPDOWN MENUS
   ========================================================================== */

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.dropdown.open .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: none;
    background: none;
    text-align: left;
    color: var(--text-secondary);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--primary-50);
    color: var(--primary-600);
}

.dropdown-item:first-child {
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-light);
    margin: var(--space-2) 0;
}

/* ==========================================================================
   PROGRESS BARS
   ========================================================================== */

.progress {
    width: 100%;
    height: 0.5rem;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

/* ==========================================================================
   TOOLTIPS
   ========================================================================== */

.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-content {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: var(--text-inverse);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    white-space: nowrap;
    z-index: var(--z-tooltip);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
}

.tooltip:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--gray-900);
}
