@model Historical_Web.Models.ViewBuilderViewModel
@{
    ViewData["Title"] = "Custom View Builder";
}

@section Styles {
    <link href="@Url.Content("~/css/view-builder.css")" rel="stylesheet" />
}

<div class="view-builder-container">
    <div id="alertArea" class="alert-area" style="display:none;"></div>
    <!-- View Configuration Card -->
    <div class="view-config-card">
        <div class="view-config-header">
            <h1 class="view-config-title">
                ⚙️ View Configuration
            </h1>
        </div>
        <div class="view-config-body">
            <form id="viewBuilderForm" class="view-form">
                <input type="hidden" id="viewId" name="viewId" />

                <div class="form-field">
                    <label for="viewName" class="field-label">
                        View Name <span class="required">*</span>
                    </label>
                    <input type="text" id="viewName" name="viewName" class="field-input" placeholder="Enter view name" required>
                </div>

                <div class="form-field">
                    <label for="tableName" class="field-label">
                        Table Name <span class="required">*</span>
                    </label>
                    <select id="tableName" name="tableName" class="field-select" required>
                        <option value="">Select a table...</option>
                        @foreach (var table in Model.AvailableTables)
                        {
                            <option value="@table">@table</option>
                        }
                    </select>
                </div>

                <div class="column-mappings-section">
                    <label class="field-label">
                        Column Mappings <span class="required">*</span>
                    </label>
                    <div class="column-mappings-container">
                        <div id="columnMappingsContainer" class="column-mappings-list">
                            <!-- Column mappings will be added here dynamically -->
                        </div>
                        <div id="emptyState" class="empty-state">
                            <p>Select a table to start adding columns</p>
                        </div>
                        <div class="add-column-section">
                            <button type="button" id="addColumnBtn" class="add-column-btn" disabled>
                                ➕ Add Column
                            </button>
                            <p class="add-column-help">Click to add a new column mapping</p>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="form-actions">
            <button type="submit" id="saveViewBtn" class="btn-save" disabled>
                💾 Save View
            </button>
            <button type="button" id="resetFormBtn" class="btn-reset">
                🔄 Reset
            </button>
            <button type="button" id="deleteViewBtn" class="btn-delete" style="display: none;">
                🗑️ Delete View
            </button>
        </div>
    </div>

    <!-- Existing Views Card -->
    <div class="existing-views-card">
        <div class="existing-views-header">
            <h2 class="existing-views-title">
                📋 Existing Views
            </h2>
        </div>
        <div class="existing-views-body">
            <div id="existingViewsList" class="existing-views-list">
                @if (Model.ExistingViews.Any())
                {
                    @foreach (var view in Model.ExistingViews)
                    {
                        <div class="view-item" data-view-id="@view.ViewID">
                            <div class="view-item-content">
                                <div class="view-item-info">
                                    <h3 class="view-item-name">@view.ViewName</h3>
                                    <p class="view-item-table">Table: @view.TableName</p>
                                    <p class="view-item-columns">
                                        @{
                                            var columnCount = view.ColumnMappingsDictionary.Count;
                                        }
                                        @columnCount column(s) mapped
                                    </p>
                                </div>
                                <div class="view-item-actions">
                                    <button type="button" class="btn-edit" data-view-id="@view.ViewID">
                                        ✏️ Edit
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="empty-views">
                        <p>No custom views created yet</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>  
        window.appConfig ={
               baseUrl: '@Url.Content("~")'
           }
    </script>
    <script src="@Url.Content("~/js/viewBuilder.js")"></script>

}
