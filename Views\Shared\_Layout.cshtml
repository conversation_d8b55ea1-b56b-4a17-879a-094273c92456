﻿@using Historical_Web.Extensions
@using Historical_Web.Models
@using Microsoft.Extensions.Options
@inject IOptions<SSOSettings> SSOOptions
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Reconciliation Historical</title>
    <link rel="icon" type="image/x-icon" href="@Url.Content("~/favicon.ico")" />
    <link href="@Url.Content("~/css/base.css")" rel="stylesheet" />
    <link href="@Url.Content("~/css/layout.css")" rel="stylesheet" />
    <link href="@Url.Content("~/css/components.css")" rel="stylesheet" />
    <link href="@Url.Content("~/lib/select2/css/select2.min.css")" rel="stylesheet" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <a href="#main" class="skip-link">Skip to main content</a>
    <header>
        <nav>
            <div>
                <span>
                    <span>Reconciliation Historical</span>
                </span>
                <button type="button" style="display: none;">
                    <span></span>
                </button>
                <div>
                    <ul>
                        <li>
                            <a asp-area="" asp-controller="TransactionInquiry" asp-action="Index">
                                <span>Home</span>
                            </a>
                        </li>
                        @{
                            // Check if user is in Finders group and hide View Builder if they are
                            var ssoSettings = SSOOptions.Value;
                            var isFinderUser = Context.Session.IsFinderUser(ssoSettings.Finders);
                        }
                        @if (!isFinderUser)
                        {
                            <li>
                                <a asp-area="" asp-controller="ViewBuilder" asp-action="Index">
                                    <span>View Builder</span>
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <div class="main-container">
        <main id="main" role="main">
            @RenderBody()
        </main>
    </div>

    <footer>
        <div>
            <div>
                <div>
                    <span>@DateTime.Now.Year - Reconciliation Historical</span>
                </div>
            </div>
        </div>
    </footer>
    <script src="@Url.Content("~/lib/jquery/dist/jquery.min.js")"></script>
    <script src="@Url.Content("~/lib/select2/js/select2.min.js")"></script>
    <script src="@Url.Content("~/site.js")"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
