/* ==========================================================================
   LAYOUT STYLES - Navigation, Header, Footer, Main Structure
   ========================================================================== */

/* ==========================================================================
   MAIN LAYOUT STRUCTURE
   ========================================================================== */

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--gray-50) 100%);
}

main {
    flex: 1;
    padding: var(--space-6) var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* ==========================================================================
   HEADER & NAVIGATION
   ========================================================================== */

header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    box-shadow: var(--shadow-sm);
}

nav {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-8);
}

nav > div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

/* Brand/Logo */
nav span:first-child {
    display: flex;
    align-items: center;
}

nav span:first-child span {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Mobile Menu Button (Hidden for desktop-only) */
nav button {
    display: none;
}

/* Navigation Menu */
nav div:last-child {
    display: flex;
}

nav ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--space-2);
}

nav li {
    margin: 0;
}

nav a {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: var(--font-medium);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

nav a:hover {
    color: var(--primary-600);
    background: var(--primary-50);
    transform: translateY(-1px);
}

nav a:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

nav a span {
    position: relative;
    z-index: 1;
}

/* Active Navigation State */
nav a.active,
nav a[aria-current="page"] {
    color: var(--primary-700);
    background: linear-gradient(135deg, var(--primary-100), var(--primary-50));
    box-shadow: var(--shadow-sm);
}

nav a.active::before,
nav a[aria-current="page"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    opacity: 0.1;
    z-index: 0;
}

/* ==========================================================================
   FOOTER
   ========================================================================== */

footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-light);
    margin-top: auto;
}

footer > div {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6) var(--space-8);
}

footer div div {
    display: flex;
    align-items: center;
    justify-content: center;
}

footer span {
    color: var(--text-tertiary);
    font-size: var(--text-sm);
    text-align: center;
}

/* ==========================================================================
   MAIN CONTENT AREA
   ========================================================================== */

.main-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-8);
    min-height: calc(100vh - 8rem);
}

.page-header {
    text-align: center;
    margin-bottom: var(--space-8);
}

.page-title {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    background: linear-gradient(135deg, var(--text-primary), var(--gray-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ==========================================================================
   RESPONSIVE BEHAVIOR (Desktop-Only Focus)
   ========================================================================== */

/* Minimum width for desktop experience */
@media (max-width: 1024px) {
    body {
        min-width: 1024px;
        overflow-x: auto;
    }
    
    .main-container {
        min-width: 1024px;
    }
}

/* Large Desktop Optimizations */
@media (min-width: 1440px) {
    main {
        padding: var(--space-8) var(--space-12);
    }
    
    nav {
        padding: 0 var(--space-12);
    }
    
    footer > div {
        padding: var(--space-6) var(--space-12);
    }
}

/* ==========================================================================
   GLASSMORPHISM EFFECTS
   ========================================================================== */

.glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.glass-overlay {
    background: var(--bg-overlay);
    backdrop-filter: blur(8px);
}

/* ==========================================================================
   LOADING STATES
   ========================================================================== */

.loading-shimmer {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* ==========================================================================
   FOCUS MANAGEMENT
   ========================================================================== */

.focus-trap {
    outline: none;
}

.focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Skip to main content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-600);
    color: var(--text-inverse);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    text-decoration: none;
    z-index: var(--z-tooltip);
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* ==========================================================================
   PRINT STYLES
   ========================================================================== */

@media print {
    header,
    footer,
    nav {
        display: none;
    }
    
    main {
        padding: 0;
        max-width: none;
        margin: 0;
    }
    
    .glass-card {
        background: white;
        backdrop-filter: none;
        border: 1px solid #ccc;
        box-shadow: none;
    }
}
