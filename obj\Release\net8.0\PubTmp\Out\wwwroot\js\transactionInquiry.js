// TransactionInquiry.js - JavaScript for the Transaction Inquiry page

// Available columns for query conditions with aliases
const availableColumns = [
    { value: 'UserText17', text: 'TerminalID' },
    { value: 'AvailOrPaid', text: 'TransactionDate' },
    { value: 'SendersReference', text: 'TraceID' },
    { value: 'UserText27', text: 'AccountReference' },
    { value: 'RelatedReference', text: 'TransactionReference' }
];

// Global variable to store current view's column mappings
let currentViewColumnMappings = {};

// Available operators for query conditions
const operators = [
    { value: '=', text: 'Equals' },
    { value: '<>', text: 'Not Equals' },
    { value: '>', text: 'Greater Than' },
    { value: '<', text: 'Less Than' },
    { value: '>=', text: 'Greater Than or Equal' },
    { value: '<=', text: 'Less Than or Equal' },
    { value: 'LIKE', text: 'Contains' },
    { value: 'IN', text: 'In List' },
];

// Date-specific operators (only show these for date columns)
const dateOperators = [
    { value: '=', text: 'Equals' },
    { value: 'BETWEEN', text: 'Between' }
];

// Logical operators for combining conditions
const logicalOperators = [
    { value: 'AND', text: 'AND' },
    { value: 'OR', text: 'OR' }
];

// Current query state
let currentPage = 1;
let pageSize = 50;
let sortColumn = 'id';
let sortDirection = 'ASC';
let selectedRows = [];
let activeColumnFilters = {};
let filterPopover = null;

// DOM Ready
$(function () {
    // Initialize the page
    initializeQueryBuilder();
    setupEventHandlers();
});

// Initialize the query builder
function initializeQueryBuilder() {
    // Add the first condition row
    addConditionRow();

    // Initialize Select2 on the main dropdowns
    initializeSelect2();

    // Hide the empty conditions message initially since we added a condition
    $('#emptyConditionsMessage').hide();
}

// Set up event handlers
function setupEventHandlers() {
    // Add condition button
    $('#addConditionBtn').on('click', addConditionRow);

    // Execute query button
    $('#executeQueryBtn').on('click', executeQuery);

    // Move to active button
    $('#moveToActiveBtn').on('click', moveToActive);

    // Row details button click handler
    $(document).on('click', '.info-btn', function() {
        const rowId = $(this).data('id');
        if (rowId) {
            showRowDetails(rowId);
        }
    });

    // Modal backdrop click handler
    $(document).on('click', '.modal-backdrop', function(e) {
        if (e.target === this) {
            hideRowDetailsModal();
        }
    });

    // Escape key handler for modal
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' && $('#rowDetailsModal').is(':visible')) {
            hideRowDetailsModal();
        }
    });

    // Checkbox change handler
    $(document).on('change', '.checkbox-input', updateSelectedRows);

    // Hide results when view or table selection changes
    $('#viewSelect, #tableSelect').on('change', function() {
        // Hide the results card when selection changes
        $('#resultsCard').hide();
        // Clear any previous results
        $('#resultsTable tbody').empty();
        $('#paginationInfo span').text('Showing 0 of 0 records');
    });

    // Handle view selection change to fetch column mappings
    $('#viewSelect').on('change', function() {
        const selectedViewId = $(this).val();
        if (selectedViewId) {
            // Fetch view details including column mappings
            fetchViewDetails(selectedViewId);
        } else {
            // Clear column mappings when no view is selected
            currentViewColumnMappings = {};
        }
    });

    // Handle column selection change to detect date columns
    $(document).on('change', '.column-select', function() {
        const selectedColumn = $(this).val();
        const columnInfo = availableColumns.find(col => col.value === selectedColumn);
        const conditionRow = $(this).closest('.condition-row');
        const valueInput = conditionRow.find('.value-input');
        const operatorSelect = conditionRow.find('.operator-select');

        // Check if the column alias contains "date" (case insensitive)
        if (columnInfo && columnInfo.text.toLowerCase().includes('date')) {
            // Convert to date input
            valueInput.attr('type', 'date');

            // Update operator dropdown to show only date operators
            operatorSelect.empty();
            operatorSelect.append('<option value="">Select Operator</option>');

            // Add date-specific operators
            dateOperators.forEach(op => {
                operatorSelect.append(`<option value="${op.value}">${op.text}</option>`);
            });

            // Refresh Select2
            operatorSelect.trigger('change');
        } else {
            // Convert back to text input
            valueInput.attr('type', 'text');

            // Restore all operators
            operatorSelect.empty();
            operatorSelect.append('<option value="">Select Operator</option>');

            // Add all operators
            operators.forEach(op => {
                operatorSelect.append(`<option value="${op.value}">${op.text}</option>`);
            });

            // Refresh Select2
            operatorSelect.trigger('change');
        }
    });

    // Handle operator selection change to show appropriate value inputs
    $(document).on('change', '.operator-select', function() {
        const selectedOperator = $(this).val();
        const conditionRow = $(this).closest('.condition-row');
        const columnSelect = conditionRow.find('.column-select');
        const selectedColumn = columnSelect.val();
        const columnInfo = availableColumns.find(col => col.value === selectedColumn);
        const isDateColumn = columnInfo && columnInfo.text.toLowerCase().includes('date');
        const valueInputContainer = conditionRow.find('.value-input').parent();

        // If it's a date column and BETWEEN operator is selected
        if (isDateColumn && selectedOperator === 'BETWEEN') {
            // Replace the single input with two date inputs
            valueInputContainer.html(`
                <div class="d-flex gap-2">
                    <input type="date" class="modern-select value-input-start" placeholder="Start date">
                    <span class="align-self-center">and</span>
                    <input type="date" class="modern-select value-input-end" placeholder="End date">
                </div>
            `);
        } else {
            // For other operators, ensure we have a single input
            if (!valueInputContainer.find('.value-input').length) {
                // If we previously had a BETWEEN operator, restore the single input
                const inputType = isDateColumn ? 'date' : 'text';
                valueInputContainer.html(`
                    <input type="${inputType}" class="modern-select value-input" placeholder="Enter value">
                `);
            }
        }
    });

    // Table header text click for sorting (only on header text, not the whole header)
    $(document).on('click', '.header-text', function(e) {
        e.stopPropagation(); // Prevent event bubbling

        const headerElement = $(this).closest('.sortable-header');
        const column = headerElement.data('column');

        // Toggle sort direction if clicking the same column
        if (column === sortColumn) {
            sortDirection = sortDirection === 'ASC' ? 'DESC' : 'ASC';
        } else {
            sortColumn = column;
            sortDirection = 'ASC';
        }

        // Update UI to show sort direction
        $('.sortable-header').removeClass('sorted-asc sorted-desc');
        if (sortDirection === 'ASC') {
            headerElement.addClass('sorted-asc');
        } else {
            headerElement.addClass('sorted-desc');
        }

        // Re-execute the query with new sort parameters
        executeQuery();
    });

    // Filter icon click for filtering (only on filter icon)
    $(document).on('click', '.filter-icon', function(e) {
        e.stopPropagation(); // Prevent event bubbling

        const column = $(this).data('column');
        const tableName = $('#tableSelect').val();
        const conditions = collectConditions();

        // Show filter popover
        showFilterPopover($(this), column, tableName, conditions);
    });

    // Handle row checkbox changes (duplicate removed - handled above with .checkbox-input)

    // Handle row click to toggle checkbox
    $(document).on('click', '#resultsTable tbody tr', function(e) {
        // Don't trigger if clicking on button, checkbox, or their containers directly
        if ($(e.target).is('button') ||
            $(e.target).closest('button').length > 0 ||
            $(e.target).is('.checkbox-input') ||
            $(e.target).closest('.checkbox-container').length > 0) {
            return;
        }

        const checkbox = $(this).find('.checkbox-input');
        if (checkbox.length > 0) {
            // Toggle checkbox state
            checkbox.prop('checked', !checkbox.prop('checked'));

            // Trigger change event to update selected rows
            checkbox.trigger('change');

            // Add visual feedback
            $(this)[0].style.backgroundColor = '#f0f0f0';
            setTimeout(() => {
                $(this)[0].style.backgroundColor = '';
            }, 200);
        }
    });

    // Handle scroll event for infinite scrolling
    $('#resultsTable').parent().on('scroll', function() {
        const scrollTop = $(this).scrollTop();
        const scrollHeight = $(this)[0].scrollHeight;
        const height = $(this).height();

        // If scrolled to bottom, load more data
        if (scrollTop + height >= scrollHeight - 50) {
            loadMoreData();
        }
    });

    // Filter event handler moved above to be more specific (.filter-icon instead of [data-column])
}

// Enhanced Select2 initialization with allowClear functionality
function initializeSelect2() {
    // Initialize Select2 on main dropdowns with allowClear
    $('#viewSelect').select2({
        placeholder: 'Choose a custom view...',
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: false,
        minimumResultsForSearch: 5
    });

    $('#tableSelect').select2({
        placeholder: 'Choose a table to query...',
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: false,
        minimumResultsForSearch: 5
    });

    // Initialize Select2 on existing condition selects
    $('.condition-select').each(function() {
        initializeConditionSelect($(this));
    });

    console.log('Select2 initialized with allowClear functionality');
}

// Initialize Select2 on individual condition selects
function initializeConditionSelect($select) {
    if ($select.hasClass('logical-operator')) {
        $select.select2({
            placeholder: 'Select Logic',
            allowClear: false,
            width: '100%',
            dropdownAutoWidth: false,
            minimumResultsForSearch: Infinity
        });
    } else if ($select.hasClass('column-select')) {
        $select.select2({
            placeholder: 'Select field',
            allowClear: true,
            width: '100%',
            dropdownAutoWidth: false,
            minimumResultsForSearch: 5
        });
    } else if ($select.hasClass('operator-select')) {
        $select.select2({
            placeholder: 'Select Operator',
            allowClear: true,
            width: '100%',
            dropdownAutoWidth: false,
            minimumResultsForSearch: Infinity
        });
    } else {
        // Default Select2 configuration
        $select.select2({
            allowClear: true,
            width: '100%',
            dropdownAutoWidth: false,
            minimumResultsForSearch: 5
        });
    }
}

// Add a new condition row to the query builder
function addConditionRow() {
    const conditionId = Date.now(); // Unique ID for the condition
    const isFirstCondition = $('#conditionsList .condition-card').length === 0;

    let html = `
        <div class="condition-card" data-id="${conditionId}">
            <div class="condition-content">
                ${!isFirstCondition ? `
                    <div class="condition-field">
                        <label class="condition-label">Logic</label>
                        <select class="condition-select logical-operator">
                            ${logicalOperators.map(op => `<option value="${op.value}">${op.text}</option>`).join('')}
                        </select>
                    </div>
                ` : ''}

                <div class="condition-field">
                    <label class="condition-label">Field</label>
                    <select class="condition-select column-select">
                        <option value="">Select field</option>
                        ${availableColumns.map(col => `<option value="${col.value}" data-alias="${col.text}">${col.text}</option>`).join('')}
                    </select>
                </div>

                <div class="condition-field">
                    <label class="condition-label">Operator</label>
                    <select class="condition-select operator-select">
                        <option value="">Select Operator</option>
                        ${operators.map(op => `<option value="${op.value}">${op.text}</option>`).join('')}
                    </select>
                </div>

                <div class="condition-field">
                    <label class="condition-label">Value</label>
                    <input type="text" class="condition-input value-input" placeholder="Enter value">
                </div>

                <div class="condition-field">
                    <button type="button" class="condition-remove remove-condition">
                        🗑️
                    </button>
                </div>
            </div>
        </div>
    `;

    $('#conditionsList').append(html);

    // Initialize Select2 on the new condition selects
    const $newCondition = $(`[data-id="${conditionId}"]`);
    $newCondition.find('.condition-select').each(function() {
        initializeConditionSelect($(this));
    });

    // Hide the empty conditions message when a condition is added
    $('#emptyConditionsMessage').hide();

    // Set up remove button handler
    $(`[data-id="${conditionId}"] .remove-condition`).on('click', function() {
        $(this).closest('.condition-card').remove();

        // Show the empty conditions message if no conditions are left
        if ($('#conditionsList .condition-card').length === 0) {
            $('#emptyConditionsMessage').show();
        }
    });

    // Set up column select change handler for date detection
    $(`[data-id="${conditionId}"] .column-select`).on('change', function() {
        const selectedColumn = $(this).val();
        const columnInfo = availableColumns.find(col => col.value === selectedColumn);
        const valueInputContainer = $(`[data-id="${conditionId}"] .condition-field`).last().prev();
        const operatorSelect = $(`[data-id="${conditionId}"] .operator-select`);

        // Check if the column alias contains "date" (case insensitive)
        if (columnInfo && columnInfo.text.toLowerCase().includes('date')) {
            // Convert to date input
            valueInputContainer.html(`
                <label class="condition-label">Value</label>
                <input type="date" class="condition-input value-input" placeholder="Select date">
            `);

            // Update operator dropdown to show only date operators
            operatorSelect.empty();
            operatorSelect.append('<option value="">Select Operator</option>');

            // Add date-specific operators
            dateOperators.forEach(op => {
                operatorSelect.append(`<option value="${op.value}">${op.text}</option>`);
            });

            operatorSelect.trigger('change');
        } else {
            // Convert back to text input
            valueInputContainer.html(`
                <label class="condition-label">Value</label>
                <input type="text" class="condition-input value-input" placeholder="Enter value">
            `);

            // Restore all operators
            operatorSelect.empty();
            operatorSelect.append('<option value="">Select Operator</option>');

            // Add all operators
            operators.forEach(op => {
                operatorSelect.append(`<option value="${op.value}">${op.text}</option>`);
            });

            operatorSelect.trigger('change');
        }
    });

    // Set up operator select change handler for BETWEEN operator
    $(`[data-id="${conditionId}"] .operator-select`).on('change', function() {
        const selectedOperator = $(this).val();
        const columnSelect = $(`[data-id="${conditionId}"] .column-select`);
        const selectedColumn = columnSelect.val();
        const columnInfo = availableColumns.find(col => col.value === selectedColumn);
        const isDateColumn = columnInfo && columnInfo.text.toLowerCase().includes('date');
        const valueInputContainer = $(`[data-id="${conditionId}"] .condition-field`).last().prev();

        // If it's a date column and BETWEEN operator is selected
        if (isDateColumn && selectedOperator === 'BETWEEN') {
            // Replace the single input with two date inputs
            valueInputContainer.html(`
                <label class="condition-label">Value</label>
                <div style="display: flex; gap: 8px; align-items: center; width: 100%;">
                    <input type="date" class="condition-input value-input-start" placeholder="Start date" style="flex: 1;">
                    <span style="color: var(--text-secondary); font-size: 12px;">to</span>
                    <input type="date" class="condition-input value-input-end" placeholder="End date" style="flex: 1;">
                </div>
            `);
        } else {
            // For other operators, ensure we have a single input
            if (!valueInputContainer.find('.value-input').length) {
                // If we previously had a BETWEEN operator, restore the single input
                if (isDateColumn) {
                    valueInputContainer.html(`
                        <label class="condition-label">Value</label>
                        <input type="date" class="condition-input value-input" placeholder="Select date">
                    `);
                } else {
                    valueInputContainer.html(`
                        <label class="condition-label">Value</label>
                        <input type="text" class="condition-input value-input" placeholder="Enter value">
                    `);
                }
            }
        }
    });
}

// Collect conditions from the UI
function collectConditions() {
    const conditions = [];
    // Collect manual conditions from UI
    $('.condition-card').each(function() {
        const column = $(this).find('.column-select').val();
        const operator = $(this).find('.operator-select').val();
        const logicalOperator = $(this).find('.logical-operator').val() || 'AND';
        const columnInfo = availableColumns.find(col => col.value === column);
        const isDateColumn = columnInfo && columnInfo.text.toLowerCase().includes('date');
        if (isDateColumn && operator === 'BETWEEN') {
            const startDate = $(this).find('.value-input-start').val();
            const endDate = $(this).find('.value-input-end').val();
            if (column && operator && startDate && endDate) {
                conditions.push({
                    column,
                    operator,
                    value: startDate + '|' + endDate,
                    logicalOperator,
                    isDateColumn,
                    isBetween: true
                });
            }
        } else {
            const value = $(this).find('.value-input').val();
            if (column && operator && value) {
                conditions.push({
                    column,
                    operator,
                    value,
                    logicalOperator,
                    isDateColumn
                });
            }
        }
    });
    // Add column filters as IN conditions (if not already present for that column)
    for (const [col, vals] of Object.entries(activeColumnFilters)) {
        if (vals && vals.length > 0 && !conditions.some(c => c.column === col)) {
            conditions.push({
                column: col,
                operator: 'IN',
                value: vals.join(','),
                logicalOperator: 'AND',
                isDateColumn: false
            });
        }
    }
    return conditions;
}

// Execute the query
function executeQuery() {
    const viewId = $('#viewSelect').val();
    const tableName = $('#tableSelect').val();
    if (!tableName) {
        showValidationError('Please select a table');
        return;
    }
    const conditions = collectConditions();
    // Allow query if there are at least two conditions (manual or column filters)
    if (conditions.length < 2) {
        showValidationError('Please add at least two conditions or use column filters');
        highlightConditionsContainer();
        return;
    }
    // Reset pagination
    currentPage = 1;
    selectedRows = [];

    // Show loading indicator
    $('#resultsTable tbody').html('<tr><td colspan="100%" class="text-center">Loading...</td></tr>');

    // Show the results card if it's hidden
    $('#resultsCard').show();

    // Execute the query
    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/TransactionInquiry/ExecuteQuery',
        type: 'POST',
        data: {
            viewId: viewId || null,
            tableName: tableName,
            conditions: JSON.stringify(conditions),
            page: currentPage,
            pageSize: pageSize,
            sortColumn: sortColumn,
            sortDirection: sortDirection
        },
        success: function(response) {
            displayResults(response);
        },
        error: function(xhr) {
            // Show validation error if it's a validation error (400 Bad Request)
            if (xhr.status === 400) {
                showValidationError(xhr.responseText);
                highlightConditionsContainer();
                $('#resultsCard').hide(); // Hide the results card
            } else {
                // Keep the results card visible and show error message for other errors
                $('#resultsCard').show();
                $('#resultsTable tbody').html(`<tr><td colspan="100%" class="text-center text-danger">Error: ${xhr.responseText}</td></tr>`);
            }
        }
    });
}

// Display query results
function displayResults(response) {
    const { data, totalRecords } = response;

    // Clear the table
    $('#resultsTable thead tr').html('<th class="actions-column"><div><span>Actions</span></div></th>');
    $('#resultsTable tbody').empty();

    // If no data, show message but keep the results card visible
    if (!data || data.length === 0) {
        $('#resultsTable tbody').html('<tr><td colspan="100%" class="text-center">No data found</td></tr>');
        $('#paginationInfo span').text('Showing 0 of 0 records');
        return;
    }

    // Add headers based on the first row
    const firstRow = data[0];
    for (const key in firstRow) {
        // Hide ID column (case-insensitive check for various ID column names)
        if (shouldHideColumn(key)) {
            continue;
        }

        // Get display name for column
        const displayName = getColumnDisplayName(key);
        const isFiltered = activeColumnFilters[key] && activeColumnFilters[key].length > 0;
        const isSorted = key === sortColumn;
        const sortClass = isSorted ? (sortDirection === 'ASC' ? 'sorted-asc' : 'sorted-desc') : '';

        $('#resultsTable thead tr').append(`
            <th data-column="${key}" class="sortable-header ${sortClass}">
                <div class="header-content">
                    <span class="header-text">${displayName}</span>
                    <button class="filter-icon ${isFiltered ? 'filtered' : ''}" data-column="${key}" title="Filter ${displayName}">
                        <svg class="filter-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"></polygon>
                        </svg>
                    </button>
                </div>
            </th>
        `);
    }

    // Add data rows
    data.forEach(row => {
        const keys = Object.keys(row);
        const actualId = row[keys[0]] || '';

        let tr = `<tr data-id="${actualId}">
            <td class="actions-cell">
                <div class="actions-container">
                    <button type="button" class="info-btn" data-id="${actualId}" title="View Details">
                        <svg class="info-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="m9,12 2,0 0,4 2,0"></path>
                            <path d="m9,8 2,0 0,2 2,0"></path>
                        </svg>
                    </button>
                    <label class="checkbox-container" title="Select Row">
                        <input type="checkbox" class="checkbox-input" data-id="${actualId}">
                        <span class="checkbox-checkmark">
                            <svg class="checkbox-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                                <polyline points="20,6 9,17 4,12"></polyline>
                            </svg>
                        </span>
                    </label>
                </div>
            </td>`;

        for (const key in row) {
            // Hide ID column (case-insensitive check for various ID column names)
            if (shouldHideColumn(key)) {
                continue;
            }
            tr += `<td>${row[key] !== null ? row[key] : ''}</td>`;
        }

        tr += '</tr>';
        $('#resultsTable tbody').append(tr);
    });

    // Update pagination info
    $('#paginationInfo span').text(`Showing ${data.length} of ${totalRecords} records`);
}

// Load more data when scrolling (infinite scroll)
function loadMoreData() {
    const viewId = $('#viewSelect').val();
    const tableName = $('#tableSelect').val();

    if (!tableName) return;

    const conditions = collectConditions();

    // Validate that at least two conditions are added
    if (conditions.length < 2) {
        showValidationError('Please add at least two conditions');
        highlightConditionsContainer();
        return;
    }

    // Increment page number
    currentPage++;

    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/TransactionInquiry/ExecuteQuery',
        type: 'POST',
        data: {
            viewId: viewId || null,
            tableName: tableName,
            conditions: JSON.stringify(conditions),
            page: currentPage,
            pageSize: pageSize,
            sortColumn: sortColumn,
            sortDirection: sortDirection
        },
        success: function(response) {
            appendResults(response);
        },
        error: function(xhr) {
            // Show validation error if it's a validation error (400 Bad Request)
            if (xhr.status === 400) {
                showValidationError(xhr.responseText);
                highlightConditionsContainer();
            }
            // Revert the page number increment since the request failed
            currentPage--;
        }
    });
}

// Append more results to the existing table
function appendResults(response) {
    const { data, totalRecords } = response;

    if (!data || data.length === 0) return;

    // Add data rows
    data.forEach(row => {
        const keys = Object.keys(row);
        const actualId = row[keys[0]] || '';

        let tr = `<tr data-id="${actualId}">
            <td class="actions-cell">
                <div class="actions-container">
                    <button type="button" class="info-btn" data-id="${actualId}" title="View Details">
                        <svg class="info-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="m9,12 2,0 0,4 2,0"></path>
                            <path d="m9,8 2,0 0,2 2,0"></path>
                        </svg>
                    </button>
                    <label class="checkbox-container" title="Select Row">
                        <input type="checkbox" class="checkbox-input" data-id="${actualId}">
                        <span class="checkbox-checkmark">
                            <svg class="checkbox-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                                <polyline points="20,6 9,17 4,12"></polyline>
                            </svg>
                        </span>
                    </label>
                </div>
            </td>`;

        for (const key in row) {
            // Hide ID column (case-insensitive check for various ID column names)
            if (shouldHideColumn(key)) {
                continue;
            }
            tr += `<td>${row[key] !== null ? row[key] : ''}</td>`;
        }

        tr += '</tr>';
        $('#resultsTable tbody').append(tr);
    });

    // Update pagination info
    const currentCount = $('#resultsTable tbody tr').length;
    $('#paginationInfo span').text(`Showing ${currentCount} of ${totalRecords} records`);
}

// Sticky column effects removed

// Update the selected rows array
function updateSelectedRows() {
    selectedRows = [];

    $('.checkbox-input:checked').each(function() {
        const id = $(this).data('id');
        if (id) {
            selectedRows.push(id);
        }
    });

    // Update the move to active button text and state
    const $moveBtn = $('#moveToActiveBtn');
    const count = selectedRows.length;

    if (count === 0) {
        $moveBtn.prop('disabled', true);
        $moveBtn.html('Move To Active');
    } else {
        $moveBtn.prop('disabled', false);
        $moveBtn.html(`Move To Active (${count})`);
    }
}

// Move selected rows to active table
function moveToActive() {
    if (selectedRows.length === 0) {
        alert('Please select at least one row');
        return;
    }

    const tableName = $('#tableSelect').val();

    if (!tableName) {
        alert('Please select a table');
        return;
    }

    if (!confirm(`Are you sure you want to move ${selectedRows.length} row(s) to the active table?`)) {
        return;
    }

    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/TransactionInquiry/MoveToActive',
        type: 'POST',
        data: {
            tableName: tableName,
            selectedIds: JSON.stringify(selectedRows)
        },
        success: function(response) {
            if (response.success) {
                let message = response.message;

                if (response.rowsProcessed > 0 && response.skippedCount > 0)
                {
                    message = `Successfully moved ${response.rowsProcessed} row(s) to the active table, And Skipped ${response.skippedCount} row(s) that were already moved.`;
                }
                else if (response.rowsProcessed > 0)
                {
                    message = `Successfully moved ${response.rowsProcessed} row(s) to the active table.`;
                }

                alert(message);
                ClearSelectedRows();

            } else {
                alert('Failed to move rows to active table');
            }
        },
        error: function(xhr) {
            alert(`Error: ${xhr.responseText}`);
        }
    });
}
function ClearSelectedRows() {
    $('#resultsTable tbody input[type="checkbox"]').prop('checked', false);
    updateSelectedRows();
}

// Show validation error message
function showValidationError(message) {
    // Create a validation error toast if it doesn't exist
    if ($('#validationToast').length === 0) {
        const toastHtml = `
            <div id="validationToast" class="toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <span id="validationMessage"></span>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        $('body').append(toastHtml);
    }

    // Set the message and show the toast
    $('#validationMessage').text(message);

    // Initialize and show the toast
    const toast = new bootstrap.Toast($('#validationToast'), {
        autohide: true,
        delay: 5000
    });
    toast.show();
}

// Highlight the conditions container to draw attention
function highlightConditionsContainer() {
    const container = $('.conditions-container');

    // Add a highlight style
    container[0].style.backgroundColor = '#fff3cd';

    // Scroll to the conditions container
    $('html, body').animate({
        scrollTop: container.offset().top - 100
    }, 500);

    // Remove the highlight after a delay
    setTimeout(() => {
        container[0].style.backgroundColor = '';
    }, 3000);
}

// Helper function to determine if a column should be hidden
function shouldHideColumn(columnName) {
    // List of column names that should be hidden (case-insensitive)
    const hiddenColumns = ['id', 'ID', 'Id', 'iD'];
    return hiddenColumns.includes(columnName);
}

// Helper function to get display name for a column
function getColumnDisplayName(columnName) {
    // First check if we have a custom alias from the selected view
    if (currentViewColumnMappings && currentViewColumnMappings[columnName]) {
        return currentViewColumnMappings[columnName];
    }

    // Fall back to the hardcoded column mappings
    const columnInfo = availableColumns.find(col => col.value === columnName);
    return columnInfo ? columnInfo.text : columnName;
}

// Fetch view details including column mappings
function fetchViewDetails(viewId) {
    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/TransactionInquiry/GetViewDetails',
        type: 'GET',
        data: { viewId: viewId },
        success: function(response) {
            // Store the column mappings globally
            currentViewColumnMappings = response.columnMappings || {};
            console.log('Loaded view column mappings:', currentViewColumnMappings);
        },
        error: function(xhr) {
            console.error('Error fetching view details:', xhr.responseText);
            // Clear mappings on error
            currentViewColumnMappings = {};
        }
    });
}

// Format column options for Select2
function formatColumnOption(option) {
    if (!option.id) {
        return option.text; // For the placeholder
    }

    // Find the column info by value
    const columnInfo = availableColumns.find(col => col.value === option.id);

    // Return the alias text if found, otherwise return the original text
    return columnInfo ? columnInfo.text : option.text;
}

function showRowDetails(rowId) {
    // Reset modal content with loading state
    $('#tab1Content, #tab2Content').html(`
        <div class="loading-state">
            <div class="spinner"></div>
            <p>Loading details...</p>
        </div>
    `);

    // Show the modal
    showRowDetailsModal();

    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + `/TransactionInquiry/GetRowDetails?id=${rowId}`,
        type: 'GET',
        success: function (response) {
            renderTab1Audit(response.tab1, rowId);
            renderTab2SourceInfo(response.tab2, rowId);
        },
        error: function (xhr) {
            const errorHtml = `
                <div class="text-center text-muted py-4">
                    <div class="display-6">⚠️</div>
                    <p class="mt-3 mb-1">Error loading details</p>
                    <small>${xhr.responseText}</small>
                </div>
            `;
            $('#tab1Content, #tab2Content').html(errorHtml);
        }
    });
}

// Show modal with animation
function showRowDetailsModal() {
    const modal = $('#rowDetailsModal');
    modal.show();
    modal.addClass('show');
    $('body').addClass('modal-open');

    // Set focus to modal for accessibility
    modal.attr('aria-hidden', 'false');
    modal.find('.modal-close').focus();
}

// Hide modal with animation
function hideRowDetailsModal() {
    const modal = $('#rowDetailsModal');
    modal.removeClass('show');
    modal.attr('aria-hidden', 'true');
    $('body').removeClass('modal-open');

    // Hide after animation completes
    setTimeout(() => {
        modal.hide();
    }, 300);
}

// Switch between tabs
function switchTab(tabId) {
    // Update tab buttons
    $('.tabs-button').attr('aria-selected', 'false').removeClass('active');
    $(`#${tabId}-tab`).attr('aria-selected', 'true').addClass('active');

    // Update tab panels
    $('.tabs-panel').hide();
    $(`#${tabId}`).show();
}

function renderTab1Audit(auditLogs, id) {
    let html = `
        <div class="timeline-header mb-3">
            <h5><i class="bi bi-clock-history me-2"></i> Transaction History <span class="text-muted">#${id}</span></h5>
        </div>`;

    if (!auditLogs || auditLogs.length === 0) {
        html += `<div class="text-center text-muted py-4">
                    <i class="bi bi-info-circle display-6"></i>
                    <p class="mt-3 mb-1">No transaction history found for this record.</p>
                    <small>Changes will appear here when they occur.</small>
                 </div>`;
        $('#tab1Content').html(html);
        return;
    }

    let currentUser = null, currentDate = null;
    auditLogs.forEach((log) => {
        const dateObj = new Date(log.effTime);
        const dateLabel = dateObj.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
        const timeLabel = dateObj.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        const isNewDate = dateLabel !== currentDate;
        const isNewUser = log.userName !== currentUser;

        if (isNewDate) {
            html += `<div class="border-bottom mt-4 mb-2 pb-1"><small class="text-muted"><i class="bi bi-calendar-event me-1"></i>${dateLabel}</small></div>`;
            currentDate = dateLabel;
        }

        if (isNewUser) {
            if (currentUser !== null) html += `</div>`; // close previous user section
            html += `<div class="card border shadow-sm mb-3">
                        <div class="card-header bg-light d-flex align-items-center">
                            <i class="bi bi-person-circle fs-4 me-2"></i>
                            <div>
                                <div class="fw-semibold">${log.userName}</div>
                                <div class="small text-muted">${timeLabel}</div>
                            </div>
                        </div>
                        <div class="card-body p-2">`;
            currentUser = log.userName;
        }

        html += `
            <div class="border-bottom pb-2 mb-2">
                <div class="d-flex justify-content-between">
                    <div><strong><i class="bi bi-tag me-1"></i>${log.fieldName || '<em>Unnamed Field</em>'}</strong></div>
                    <small class="text-muted"><i class="bi bi-clock me-1"></i>${log.effTime}</small>
                </div>
                <div class="mt-2 row">
                    <div class="col-5">
                        <div class="text-muted small">From</div>
                        <div class="border rounded p-2 bg-light">${log.oldValue || '<span class="text-muted">null</span>'}</div>
                    </div>
                    <div class="col-2 d-flex justify-content-center align-items-center">
                        <i class="bi bi-arrow-right fs-4 text-secondary"></i>
                    </div>
                    <div class="col-5">
                        <div class="text-muted small">To</div>
                        <div class="border rounded p-2 bg-light">${log.newValue || '<span class="text-muted">null</span>'}</div>
                    </div>
                </div>
            </div>`;
    });

    html += `</div></div>`; // Close last card
    $('#tab1Content').html(html);
}

function renderTab2SourceInfo(records, id) {
    let html = `
        <div class="timeline-header mb-3">
            <h5><i class="bi bi-database me-2"></i> Source Information <span class="text-muted">#${id}</span></h5>
        </div>`;

    if (!records || records.length === 0) {
        html += `<div class="text-center text-muted py-4">
                    <i class="bi bi-info-circle display-6"></i>
                    <p class="mt-3 mb-1">No source information found for this record.</p>
                    <small>This record has not been moved to active status.</small>
                 </div>`;
        $('#tab2Content').html(html);
        return;
    }

    records.forEach(record => {
        const insertedDate = new Date(record.insertedAt);
        const dateLabel = insertedDate.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
        const timeLabel = insertedDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });

        html += `
            <div class="card shadow-sm border mb-3">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <i class="bi bi-person-circle fs-4 me-2"></i>
                        <div>
                            <div class="fw-semibold">${record.userID || "Unknown User"}</div>
                            <small class="text-muted">${timeLabel} • ${dateLabel}</small>
                        </div>
                    </div>
                    <div class="bg-light border rounded p-3">
                        <div><i class="bi bi-arrow-right-circle me-2"></i>Moved to Active</div>
                        <div class="mt-2">
                            <strong>From ID:</strong> <code>${record.originalID}</code><br>
                            <strong>To ID:</strong> <code>${record.insertedID}</code>
                        </div>
                    </div>
                </div>
            </div>`;
    });

    $('#tab2Content').html(html);
}

function showFilterPopover($icon, column, tableName, conditions) {
    // Remove any existing filter popup
    hideFilterPopup();

    // Add active state to icon
    $icon.addClass('active');

    // Create enhanced filter popup
    const popupId = 'filter-popup-' + column;
    const popup = createEnhancedFilterPopup(popupId, column);

    // Position the popup
    positionFilterPopup(popup, $icon);

    // Store reference
    filterPopover = {
        popup: popup,
        icon: $icon,
        column: column,
        dispose: function() {
            hideFilterPopup();
        }
    };

    // Show loading state
    showFilterLoading(popup);

    // Fetch distinct values
    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/TransactionInquiry/GetDistinctColumnValues',
        type: 'POST',
        data: {
            tableName: tableName,
            columnName: column,
            conditions: JSON.stringify(conditions)
        },
        success: function(values) {
            renderEnhancedFilterDropdown(popup, column, values);
        },
        error: function() {
            showFilterError(popup, 'Failed to load filter values');
        }
    });
}

function createEnhancedFilterPopup(popupId, column) {
    const popup = $(`
        <div id="${popupId}" class="filter-popup">
            <div class="filter-popup-header">
                <h6 class="filter-popup-title">
                    <i class="bi bi-funnel-fill"></i>
                    Filter: ${column}
                </h6>
                <button type="button" class="filter-popup-close">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="filter-popup-body">
                <div class="filter-controls-section">
                    <div class="filter-bulk-actions">
                        <button type="button" class="filter-bulk-btn select-all-btn">Select All</button>
                        <button type="button" class="filter-bulk-btn deselect-all-btn">Deselect All</button>
                    </div>
                    <div class="filter-count-info">
                        <span class="selected-count">0</span> of <span class="total-count">0</span> selected
                    </div>
                </div>
                <div class="filter-values-section">
                    <div class="filter-values-list">
                        <!-- Values will be populated here -->
                    </div>
                </div>
            </div>
            <div class="filter-popup-footer">
                <div class="filter-footer-info">
                    <span class="filter-status-text">Ready to apply</span>
                </div>
                <div class="filter-footer-actions">
                    <button type="button" class="filter-btn filter-btn-secondary clear-filter-btn" data-column="${column}">
                        <i class="bi bi-x-circle"></i> Clear
                    </button>
                    <button type="button" class="filter-btn filter-btn-primary apply-filter-btn" data-column="${column}">
                        <i class="bi bi-check-circle"></i> Apply
                    </button>
                </div>
            </div>
        </div>
    `);

    $('body').append(popup);

    // Setup event handlers
    setupFilterPopupEvents(popup, column);

    return popup;
}

function positionFilterPopup(popup, $icon) {
    const iconOffset = $icon.offset();
    const iconHeight = $icon.outerHeight();
    const iconWidth = $icon.outerWidth();
    const popupWidth = popup.outerWidth();
    const popupHeight = popup.outerHeight();
    const windowWidth = $(window).width();
    const windowHeight = $(window).height();
    const scrollTop = $(window).scrollTop();

    let left = iconOffset.left - (popupWidth / 2) + (iconWidth / 2);
    let top = iconOffset.top + iconHeight + 8;

    // Adjust horizontal position if popup goes off screen
    if (left < 10) {
        left = 10;
    } else if (left + popupWidth > windowWidth - 10) {
        left = windowWidth - popupWidth - 10;
    }

    // Adjust vertical position if popup goes off screen
    if (top + popupHeight > windowHeight + scrollTop - 10) {
        top = iconOffset.top - popupHeight - 8;
    }

    popup.css({
        position: 'absolute',
        left: left + 'px',
        top: top + 'px',
        zIndex: 2000
    });
}

function setupFilterPopupEvents(popup, column) {
    // Close button
    popup.find('.filter-popup-close').on('click', function() {
        hideFilterPopup();
    });

    // Search functionality removed per user request

    // Bulk actions
    popup.find('.select-all-btn').on('click', function() {
        selectAllVisibleValues(popup, true);
    });

    popup.find('.deselect-all-btn').on('click', function() {
        selectAllVisibleValues(popup, false);
    });

    // Footer actions
    popup.find('.clear-filter-btn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Clear filter clicked for column:', column);
        clearColumnFilter(column);
        hideFilterPopup();
    });

    popup.find('.apply-filter-btn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Apply filter clicked for column:', column);
        applyColumnFilter(popup, column);
        hideFilterPopup();
    });

    // Prevent popup from closing when clicking inside
    popup.on('click', function(e) {
        e.stopPropagation();
    });
}

function renderEnhancedFilterDropdown(popup, column, values) {
    const selected = activeColumnFilters[column] || [];
    const valuesList = popup.find('.filter-values-list');

    // Clear existing content
    valuesList.empty();

    if (values.length === 0) {
        showFilterEmptyState(popup);
        return;
    }

    // Render values
    values.forEach((item, index) => {
        const safeVal = item.value === null ? '(Empty)' : item.value;
        const displayVal = item.display ?? safeVal;
        const isChecked = selected.includes(item.value);
        const itemId = `filter-${column}-${index}`;

        const valueItem = $(`
            <div class="filter-value-item" data-value="${item.value ?? ''}" data-display="${displayVal}">
                <input type="checkbox" class="filter-value-checkbox"
                       id="${itemId}"
                       ${isChecked ? 'checked' : ''}>
                <label class="filter-value-label" for="${itemId}">${displayVal}</label>
                <span class="filter-value-count">${item.count || 0}</span>
            </div>
        `);

        // Add click handler for the entire item
        valueItem.on('click', function(e) {
            if (e.target.type !== 'checkbox') {
                const checkbox = $(this).find('.filter-value-checkbox');
                checkbox.prop('checked', !checkbox.prop('checked'));
            }
            updateFilterCounts(popup);
        });

        // Add change handler for checkbox
        valueItem.find('.filter-value-checkbox').on('change', function() {
            updateFilterCounts(popup);
        });

        valuesList.append(valueItem);
    });

    // Update counts
    updateFilterCounts(popup);

    // Update total count
    popup.find('.total-count').text(values.length);
}

function showFilterLoading(popup) {
    const valuesList = popup.find('.filter-values-list');
    valuesList.html(`
        <div class="filter-loading-state">
            <div class="filter-loading-spinner"></div>
            <p>Loading filter values...</p>
        </div>
    `);
}

function showFilterError(popup, message) {
    const valuesList = popup.find('.filter-values-list');
    valuesList.html(`
        <div class="filter-empty-state">
            <div class="filter-empty-icon">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <p class="filter-empty-text">${message}</p>
        </div>
    `);
}

function showFilterEmptyState(popup) {
    const valuesList = popup.find('.filter-values-list');
    valuesList.html(`
        <div class="filter-empty-state">
            <div class="filter-empty-icon">
                <i class="bi bi-inbox"></i>
            </div>
            <p class="filter-empty-text">No values found</p>
        </div>
    `);
}

// Search functions removed per user request

function selectAllVisibleValues(popup, select) {
    const allCheckboxes = popup.find('.filter-value-checkbox');
    allCheckboxes.prop('checked', select);
    updateFilterCounts(popup);
}

function updateFilterCounts(popup) {
    const totalCheckboxes = popup.find('.filter-value-checkbox');
    const checkedCheckboxes = popup.find('.filter-value-checkbox:checked');

    popup.find('.selected-count').text(checkedCheckboxes.length);
    popup.find('.total-count').text(totalCheckboxes.length);

    // Update status text
    const statusText = popup.find('.filter-status-text');
    if (checkedCheckboxes.length === 0) {
        statusText.text('No values selected');
    } else if (checkedCheckboxes.length === totalCheckboxes.length) {
        statusText.text('All values selected');
    } else {
        statusText.text(`${checkedCheckboxes.length} values selected`);
    }
}

function applyColumnFilter(popup, column) {
    console.log('applyColumnFilter called for column:', column);

    const checkedValues = popup.find('.filter-value-checkbox:checked').map(function() {
        return $(this).closest('.filter-value-item').data('value');
    }).get();

    console.log('Checked values:', checkedValues);

    activeColumnFilters[column] = checkedValues;
    updateFiltersFromColumnFilters();
    executeQuery();

    // Update filter icon state
    const filterIcon = $(`[data-column="${column}"]`);
    if (checkedValues.length > 0) {
        filterIcon[0].style.color = 'blue';
    } else {
        filterIcon[0].style.color = '';
    }

    console.log('Filter applied successfully');
}

function clearColumnFilter(column) {
    console.log('clearColumnFilter called for column:', column);

    activeColumnFilters[column] = [];
    updateFiltersFromColumnFilters();
    executeQuery();

    // Update filter icon state
    const filterIcon = $(`button[data-column="${column}"]`);
    filterIcon[0].style.color = '';

    console.log('Filter cleared successfully');
}

function updateFiltersFromColumnFilters() {
    // This function updates the UI to reflect the current column filters
    // It's called after applying or clearing column filters

    // Update filter icons to show active state
    $('[data-column]').each(function() {
        const column = $(this).data('column');
        const hasActiveFilter = activeColumnFilters[column] && activeColumnFilters[column].length > 0;

        if (hasActiveFilter) {
            $(this)[0].style.color = 'blue';
        } else {
            $(this)[0].style.color = '';
        }
    });

    // Note: The actual filter conditions are handled in collectConditions()
    // which automatically includes activeColumnFilters as IN conditions
}

function hideFilterPopup() {
    if (filterPopover && filterPopover.popup) {
        filterPopover.popup.remove();
        if (filterPopover.icon) {
            filterPopover.icon[0].style.backgroundColor = '';
        }
        filterPopover = null;
    }
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Legacy function for backward compatibility
function renderFilterDropdown(_$icon, column, values) {
    // This function is kept for backward compatibility
    // but the enhanced version should be used instead
    renderEnhancedFilterDropdown(filterPopover.popup, column, values);
}

// Enhanced event handlers for the new filter popup

// Hide popup on outside click
$(document).on('click', function(e) {
    if (filterPopover && !$(e.target).closest('.filter-popup').length) {
        hideFilterPopup();
    }
});

// Keyboard navigation support
$(document).on('keydown', function(e) {
    if (!filterPopover) return;

    const popup = filterPopover.popup;

    switch(e.key) {
        case 'Escape':
            e.preventDefault();
            hideFilterPopup();
            break;

        case 'Enter':
            if (popup.find('.apply-filter-btn').is(':focus')) {
                e.preventDefault();
                popup.find('.apply-filter-btn').click();
            }
            break;

        case 'ArrowDown':
            if (e.target.classList.contains('filter-value-checkbox')) {
                e.preventDefault();
                const nextItem = $(e.target).closest('.filter-value-item').next('.filter-value-item').find('.filter-value-checkbox');
                if (nextItem.length) {
                    nextItem[0].focus();
                }
            }
            break;

        case 'ArrowUp':
            if (e.target.classList.contains('filter-value-checkbox')) {
                e.preventDefault();
                const prevItem = $(e.target).closest('.filter-value-item').prev('.filter-value-item').find('.filter-value-checkbox');
                if (prevItem.length) {
                    prevItem[0].focus();
                }
            }
            break;

        case ' ':
            if (e.target.classList.contains('filter-value-checkbox')) {
                e.preventDefault();
                $(e.target).prop('checked', !$(e.target).prop('checked')).trigger('change');
            }
            break;
    }
});

// Window resize handler to reposition popup
$(window).on('resize', function() {
    if (filterPopover && filterPopover.popup && filterPopover.icon) {
        positionFilterPopup(filterPopover.popup, filterPopover.icon);
    }
});

// Legacy event handlers for backward compatibility (now handled by enhanced popup)
$(document).on('click', '.apply-filter-btn[data-column]', function() {
    const column = $(this).data('column');
    if (column && filterPopover) {
        applyColumnFilter(filterPopover.popup, column);
        hideFilterPopup();
    }
});

$(document).on('click', '.clear-filter-btn[data-column]', function() {
    const column = $(this).data('column');
    if (column) {
        clearColumnFilter(column);
        hideFilterPopup();
    }
});