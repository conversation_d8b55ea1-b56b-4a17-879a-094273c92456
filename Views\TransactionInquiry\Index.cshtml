@model Historical_Web.Models.TransactionInquiryViewModel

@{
    ViewData["Title"] = "Transaction Inquiry";
}

@section Styles {
    <link href="@Url.Content("~/css/transaction-inquiry.css")" rel="stylesheet" />
}

<div class="transaction-inquiry-container">
    <!-- Query Builder Card -->
    <div class="query-builder-card">
        <div class="query-builder-body">
            <!-- View & Table Selection -->
            <div class="selection-grid">
                <div class="selection-field">
                    <label for="viewSelect" class="field-label">
                        <span>Select View</span>
                        <span class="optional">(Optional)</span>
                    </label>
                    <div class="field-wrapper">
                        <select id="viewSelect" class="field-select">
                            <option value="">Choose a custom view...</option>
                            @foreach (var view in Model.AvailableViews)
                            {
                                <option value="@view.ViewID">@view.ViewName</option>
                            }
                        </select>
                    </div>
                    <div class="field-help">
                        Select a predefined view or leave empty to use table columns
                    </div>
                </div>
                <div class="selection-field">
                    <label for="tableSelect" class="field-label">
                        <span>Select Table</span>
                        <span class="required">*</span>
                    </label>
                    <div class="field-wrapper">
                        <select id="tableSelect" class="field-select" required>
                            <option value="">Choose a table to query...</option>
                            @foreach (var table in Model.AvailableTables)
                            {
                                <option value="@table">@table</option>
                            }
                        </select>
                    </div>
                    <div class="field-help">
                        Required: Select the table to search
                    </div>
                </div>
            </div>

            <!-- Query Conditions Section -->
            <div class="conditions-section">
                <div class="conditions-header">
                    <h2 class="conditions-title">Query Conditions</h2>
                </div>
                <div class="conditions-container">
                    <div id="conditionsList" class="conditions-list">
                        <!-- Conditions will be added here dynamically -->
                    </div>
                    <div id="emptyConditionsMessage" class="empty-conditions">
                        <h6>No Conditions Added</h6>
                        <p>Add at least one condition to execute your query</p>
                        <div class="help-text">
                            Click "Add Condition" to start building your query
                        </div>
                    </div>
                    <button type="button" id="addConditionBtn" class="add-condition-btn">
                        <span>➕ Add Condition</span>
                    </button>
                </div>
            </div>

        </div>
        <!-- Query Actions -->
        <div class="query-actions">
            <div class="query-tip">
                💡 <span>Tip: Use multiple conditions to refine your search results</span>
            </div>
            <button type="button" id="executeQueryBtn" class="execute-query-btn">
                <span>🚀 Run Query</span>
            </button>
        </div>
    </div>

    <!-- Results Card -->
    <div id="resultsCard" class="results-card" style="display: none;">
        <div class="results-table-container">
            <table id="resultsTable" class="results-table">
                <thead>
                    <tr>
                        <th class="actions-column">
                            <div>
                                <span>Actions</span>
                            </div>
                        </th>
                        <!-- Table headers will be added dynamically -->
                    </tr>
                </thead>
                <tbody>
                    <!-- Table data will be added dynamically -->
                </tbody>
            </table>
        </div>
        <div class="results-footer">
            <div class="results-info">
                <div id="paginationInfo" class="pagination-info">
                    <span>Showing 0 of 0 records</span>
                </div>
                <div id="selectionInfo" class="selection-info" style="display: none;">
                    <span id="selectedCount">0</span> selected
                </div>
            </div>
            <div>
                <button type="button" id="moveToActiveBtn" class="move-to-active-btn" disabled>
                    <span>📤 Move To Active</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Details Modal -->
<div id="rowDetailsModal" class="modal-backdrop" style="display: none;" tabindex="-1" aria-labelledby="rowDetailsModalLabel" aria-hidden="true">
    <div class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <div class="modal-title-section">
                    <h5 id="rowDetailsModalLabel" class="modal-title">
                        📋 Transaction Details
                    </h5>
                    <p class="modal-subtitle">Detailed information and audit trail</p>
                </div>
                <button type="button" class="modal-close" onclick="hideRowDetailsModal()" aria-label="Close">
                    ✕
                </button>
            </div>

            <div class="modal-body">
                <div class="tabs">
                    <ul id="detailsTabs" class="tabs-list" role="tablist">
                        <li class="tabs-item" role="presentation">
                            <button id="tab1-tab" class="tabs-button" onclick="switchTab('tab1')" type="button" role="tab" aria-controls="tab1" aria-selected="true">
                                📊 Transaction History
                            </button>
                        </li>
                        <li class="tabs-item" role="presentation">
                            <button id="tab2-tab" class="tabs-button" onclick="switchTab('tab2')" type="button" role="tab" aria-controls="tab2" aria-selected="false">
                                📄 Source Information
                            </button>
                        </li>
                    </ul>
                </div>

                <div id="detailsTabContent" class="tabs-content">
                    <div id="tab1" class="tabs-panel" role="tabpanel" aria-labelledby="tab1-tab">
                        <div id="tab1Content" class="tab-content-area">
                            <div class="loading-state">
                                <div class="spinner"></div>
                                <p>Loading transaction details...</p>
                            </div>
                        </div>
                    </div>
                    <div id="tab2" class="tabs-panel" role="tabpanel" aria-labelledby="tab2-tab" style="display: none;">
                        <div id="tab2Content" class="tab-content-area">
                            <div class="loading-state">
                                <div class="spinner"></div>
                                <p>Loading additional information...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideRowDetailsModal()">
                    🔙 Close
                </button>
            </div>
        </div>
    </div>
</div>

<div id="alertArea" class="alert-area" style="display:none;"></div>

@section Scripts {
    <script> window.appConfig ={
        baseUrl: '@Url.Content("~")'
    }
    </script>
    <script src="@Url.Content("~/js/transactionInquiry.js")"></script>
}
