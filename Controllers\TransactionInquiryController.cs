using System.Data;
using System.Diagnostics;
using Historical_Web.Data;
using Historical_Web.Extensions;
using Historical_Web.Models;
using Historical_Web.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace Historical_Web.Controllers
{
    public class TransactionInquiryController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly DatabaseService _databaseService;
        private readonly ILogger<TransactionInquiryController> _logger;

        public TransactionInquiryController(
            ApplicationDbContext context,
            DatabaseService databaseService,
            ILogger<TransactionInquiryController> logger)
        {
            _context = context;
            _databaseService = databaseService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var viewModel = new TransactionInquiryViewModel
                {
                    AvailableViews = await _context.GridViews.ToListAsync(),
                    AvailableTables = await _databaseService.GetTableNamesAsync()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading TransactionInquiry page");
                return View("Error", new ErrorViewModel { RequestId = HttpContext.TraceIdentifier });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ExecuteQuery(
            int? viewId,
            string tableName,
            string conditions,
            int page = 1,
            int pageSize = 50,
            string sortColumn = "id",
            string sortDirection = "ASC")
        {
            try
            {
                if (string.IsNullOrEmpty(tableName))
                {
                    return BadRequest("Table name is required");
                }

                // Parse conditions from JSON
                var queryConditions = string.IsNullOrEmpty(conditions)
                    ? new List<QueryCondition>()
                    : JsonConvert.DeserializeObject<List<QueryCondition>>(conditions) ?? new List<QueryCondition>();

                // Validate that at least two conditions are provided
                if (queryConditions.Count < 2)
                {
                    return BadRequest("At least two conditions are required");
                }

                // Get the selected view if any
                GridView? selectedView = null;
                if (viewId.HasValue)
                {
                    selectedView = await _context.GridViews.FindAsync(viewId.Value);
                }

                // Execute the query
                var results = await _databaseService.ExecuteQueryAsync(
                    tableName,
                    queryConditions,
                    selectedView,
                    page,
                    pageSize,
                    sortColumn,
                    sortDirection);

                // Get total records for pagination
                int totalRecords = Convert.ToInt32(results.ExtendedProperties["TotalRecords"]);

                // Convert DataTable to a format suitable for JSON
                var jsonData = new
                {
                    data = DataTableToJson(results),
                    totalRecords,
                    page,
                    pageSize,
                    sortColumn,
                    sortDirection
                };

                return Json(jsonData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing query");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> MoveToActive(string tableName, string selectedIds)
        {
            try
            {
                if (string.IsNullOrEmpty(tableName))
                {
                    return BadRequest("Table name is required");
                }

                if (string.IsNullOrEmpty(selectedIds))
                {
                    return BadRequest("No rows selected");
                }

                // Parse selected IDs
                var ids = JsonConvert.DeserializeObject<List<int>>(selectedIds) ?? new List<int>();
                if (ids.Count == 0)
                {
                    return BadRequest("No valid IDs provided");
                }

                string currentUser = HttpContext.Session?.GetUserId()
                            ?? "UnknownUser";

                var result = await _databaseService.MoveToActiveAsync(tableName, ids, currentUser);

                return Json(new {
                    success = true,
                    rowsAffected = result.RowsProcessed,
                    rowsProcessed = result.RowsProcessed,
                    skippedCount = result.SkippedIds.Count,
                    skippedIds = result.SkippedIds,
                    processedIds = result.ProcessedIds,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error moving rows to active table");
                return StatusCode(500, new { error = ex.Message });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetRowDetails(int id, string sourceTable)
        {
            try
            {
                // Allow negative IDs since we now support them in the enhanced queries
                if (id == 0)
                    return BadRequest("Invalid ID");

                if (string.IsNullOrEmpty(sourceTable))
                    return BadRequest("Source table name is required");

                var result = await _databaseService.GetRowDetailsAsync(id, sourceTable);
                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get row details");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetViewDetails(int viewId)
        {
            try
            {
                var view = await _context.GridViews.FindAsync(viewId);
                if (view == null)
                    return NotFound();

                var columnMappings = new Dictionary<string, string>();
                if (!string.IsNullOrEmpty(view.ColumnMappings))
                {
                    columnMappings = JsonConvert.DeserializeObject<Dictionary<string, string>>(view.ColumnMappings)
                        ?? new Dictionary<string, string>();
                }

                var result = new
                {
                    viewId = view.ViewID,
                    viewName = view.ViewName,
                    tableName = view.TableName,
                    columnMappings = columnMappings
                };

                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting view details for {ViewId}", viewId);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetDistinctColumnValues(string tableName, string columnName, string conditions)
        {
            try
            {
                if (string.IsNullOrEmpty(tableName) || string.IsNullOrEmpty(columnName))
                {
                    return BadRequest("Table name and column name are required");
                }
                var queryConditions = string.IsNullOrEmpty(conditions)
                    ? new List<QueryCondition>()
                    : JsonConvert.DeserializeObject<List<QueryCondition>>(conditions) ?? new List<QueryCondition>();
                var values = await _databaseService.GetDistinctColumnValuesAsync(tableName, columnName, queryConditions);
                return Json(values);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching distinct column values");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        private List<Dictionary<string, object>> DataTableToJson(DataTable table)
        {
            var list = new List<Dictionary<string, object>>();

            foreach (DataRow row in table.Rows)
            {
                var dict = new Dictionary<string, object>();

                foreach (DataColumn col in table.Columns)
                {
                    dict[col.ColumnName] = row[col] == DBNull.Value ? null! : row[col];
                }

                list.Add(dict);
            }

            return list;
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}

