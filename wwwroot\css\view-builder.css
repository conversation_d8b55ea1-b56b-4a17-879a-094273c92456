/* ==========================================================================
   VIEW BUILDER PAGE STYLES
   ========================================================================== */

/* ==========================================================================
   PAGE LAYOUT
   ========================================================================== */

.view-builder-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
    align-items: start;
}

/* ==========================================================================
   VIEW CONFIGURATION CARD
   ========================================================================== */

.view-config-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: sticky;
    top: var(--space-6);
}

.view-config-header {
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--border-light);
}

.view-config-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.view-config-body {
    padding: var(--space-8);
}

/* ==========================================================================
   FORM STYLING
   ========================================================================== */

.view-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.field-label {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.field-label .required {
    color: var(--error);
    font-weight: var(--font-bold);
}

.field-input,
.field-select {
    width: 100%;
    padding: var(--space-4) var(--space-5);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-xl);
    font-size: var(--text-base);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.field-input:focus,
.field-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 4px rgba(168, 85, 247, 0.1);
    transform: translateY(-2px);
}

.field-input::placeholder {
    color: var(--text-tertiary);
}

/* ==========================================================================
   SELECT2 ENHANCED DROPDOWN STYLING
   ========================================================================== */

/* Field Select Specific Styling */
.field-select.select2-hidden-accessible + .select2-container .select2-selection--single {
    padding: var(--space-4) var(--space-5);
    border: 2px solid var(--border-light) !important;
    border-radius: var(--radius-xl) !important;
    font-size: var(--text-base);
    min-height: 3.5rem;
    box-shadow: var(--shadow-sm);
}

.field-select.select2-hidden-accessible + .select2-container .select2-selection--single:focus,
.field-select.select2-hidden-accessible + .select2-container.select2-container--focus .select2-selection--single {
    border-color: var(--primary-500) !important;
    box-shadow: 0 0 0 4px rgba(168, 85, 247, 0.1) !important;
    transform: translateY(-2px);
}

/* Column Select Specific Styling */
.column-select.select2-hidden-accessible + .select2-container .select2-selection--single {
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-medium) !important;
    border-radius: var(--radius-lg) !important;
    font-size: var(--text-sm);
    min-height: 2.75rem;
}

.column-select.select2-hidden-accessible + .select2-container .select2-selection--single:focus,
.column-select.select2-hidden-accessible + .select2-container.select2-container--focus .select2-selection--single {
    border-color: var(--primary-500) !important;
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1) !important;
    transform: none;
}

/* ==========================================================================
   COLUMN MAPPINGS SECTION
   ========================================================================== */

.column-mappings-section {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.column-mappings-container {
    background: var(--gray-50);
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    min-height: 300px;
    position: relative;
}

.column-mappings-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

/* Individual Column Mapping */
.column-mapping {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
}

.column-mapping:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.column-mapping-content {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: var(--space-4);
    align-items: end;
}

.column-field {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.column-label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
}

.column-select,
.column-input {
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    background: var(--bg-primary);
    transition: all var(--transition-fast);
}

.column-select:focus,
.column-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
}

.column-remove {
    background: var(--error);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-2);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
}

.column-remove:hover {
    background: #dc2626;
    transform: scale(1.05);
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--space-8);
    color: var(--text-tertiary);
}

.empty-state p {
    font-size: var(--text-base);
    margin: 0;
}

/* Add Column Section */
.add-column-section {
    background: var(--primary-50);
    border: 1px solid var(--primary-200);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.add-column-btn {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-5);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.add-column-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
}

.add-column-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.add-column-help {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* ==========================================================================
   FORM ACTIONS
   ========================================================================== */

.form-actions {
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
    padding: var(--space-6) var(--space-8);
    border-top: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.btn-save {
    background: linear-gradient(135deg, var(--success), #059669);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.btn-save:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
}

.btn-save:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-reset {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-reset:hover {
    background: var(--gray-50);
    color: var(--text-primary);
    border-color: var(--border-dark);
}

.btn-delete {
    background: linear-gradient(135deg, var(--error), #dc2626);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    margin-left: auto;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
}

/* ==========================================================================
   EXISTING VIEWS CARD
   ========================================================================== */

.existing-views-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.existing-views-header {
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--border-light);
}

.existing-views-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.existing-views-body {
    padding: var(--space-6);
    max-height: 600px;
    overflow-y: auto;
}

.existing-views-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

/* Individual View Item */
.view-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.view-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: var(--primary-300);
}

.view-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.view-item-info {
    flex: 1;
}

.view-item-name {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-1) 0;
}

.view-item-table {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--space-1) 0;
}

.view-item-columns {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    margin: 0;
}

.view-item-actions {
    display: flex;
    gap: var(--space-2);
}

.btn-edit {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-edit:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
}

/* Empty Views State */
.empty-views {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--space-12);
    color: var(--text-tertiary);
}

.empty-views p {
    font-size: var(--text-base);
    margin: 0;
}

/* ==========================================================================
   RESPONSIVE ADJUSTMENTS
   ========================================================================== */

@media (max-width: 1200px) {
    .view-builder-container {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .view-config-card {
        position: static;
    }
}
