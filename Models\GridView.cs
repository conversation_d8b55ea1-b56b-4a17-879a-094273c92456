using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;

namespace Historical_Web.Models
{
    [Table("GridViews")]
    public class GridView
    {
        [Key]
        public int ViewID { get; set; }
        
        public string ViewName { get; set; } = string.Empty;
        
        public string TableName { get; set; } = string.Empty;
        
        public string ColumnMappings { get; set; } = string.Empty;
        
        [NotMapped]
        public Dictionary<string, string> ColumnMappingsDictionary
        {
            get
            {
                if (string.IsNullOrEmpty(ColumnMappings))
                    return new Dictionary<string, string>();
                
                return JsonConvert.DeserializeObject<Dictionary<string, string>>(ColumnMappings) 
                    ?? new Dictionary<string, string>();
            }
        }
    }
}
