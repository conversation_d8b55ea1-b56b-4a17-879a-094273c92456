﻿@model ErrorViewModel
@{
    ViewData["Title"] = "Error";
}

@section Styles {
    <link href="@Url.Content("~/css/error.css")" rel="stylesheet" />
}

<div class="error-container">
    <div class="error-card">
        <div class="error-icon"></div>

        <h1 class="error-title">Oops! Something went wrong</h1>
        <h2 class="error-subtitle">An error occurred while processing your request.</h2>

        <p class="error-description">
            We apologize for the inconvenience. Our team has been notified and is working to resolve this issue.
        </p>

        @if (Model.ShowRequestId)
        {
            <div class="error-details">
                <h3>Error Details</h3>
                <p>
                    <strong>Request ID:</strong> <code>@Model.RequestId</code>
                </p>
                <p>Please include this Request ID when contacting support for faster assistance.</p>
            </div>
        }

        <div class="development-info">
            <h3>Development Mode</h3>
            <p>
                Swapping to <strong>Development</strong> environment will display more detailed information about the error that occurred.
            </p>
            <p>
                <strong>The Development environment shouldn't be enabled for deployed applications.</strong>
                It can result in displaying sensitive information from exceptions to end users.
                For local debugging, enable the <strong>Development</strong> environment by setting the <strong>ASPNETCORE_ENVIRONMENT</strong> environment variable to <strong>Development</strong>
                and restarting the app.
            </p>
        </div>

        <div class="error-actions">
            <div class="btn-group">
                <a href="/" class="btn-home">
                    🏠 Go Home
                </a>
                <button onclick="history.back()" class="btn-back">
                    ← Go Back
                </button>
            </div>
            <p class="error-help">
                If this problem persists, please contact our support team.
            </p>
        </div>
    </div>
</div>
