using System.Data;

namespace Historical_Web.Models
{
    public class TransactionInquiryViewModel
    {
        public List<GridView> AvailableViews { get; set; } = new List<GridView>();
        public List<string> AvailableTables { get; set; } = new List<string>();
        public int? SelectedViewId { get; set; }
        public string? SelectedTable { get; set; }
        public List<QueryCondition> Conditions { get; set; } = new List<QueryCondition>();
        public DataTable? Results { get; set; }
        public int TotalRecords { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string SortColumn { get; set; } = "id";
        public string SortDirection { get; set; } = "ASC";
    }
}
