﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SSOServiceReference
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UserInfo", Namespace="http://tempuri.org/")]
    public partial class UserInfo : object
    {
        
        private string UserIDField;
        
        private string CompanyIDUniqueField;
        
        private string CompanyCodeField;
        
        private string CompanyNameField;
        
        private string BranchIDField;
        
        private string FlexBranchCodeField;
        
        private string BranchNameField;
        
        private string OfficeIDField;
        
        private string OfficeNameField;
        
        private string UserNameField;
        
        private int UserTypeIDField;
        
        private string UserTypeNameField;
        
        private SSOServiceReference.HubBranches[] SMEsHubBranchesField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string UserID
        {
            get
            {
                return this.UserIDField;
            }
            set
            {
                this.UserIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string CompanyIDUnique
        {
            get
            {
                return this.CompanyIDUniqueField;
            }
            set
            {
                this.CompanyIDUniqueField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string CompanyCode
        {
            get
            {
                return this.CompanyCodeField;
            }
            set
            {
                this.CompanyCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string CompanyName
        {
            get
            {
                return this.CompanyNameField;
            }
            set
            {
                this.CompanyNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string BranchID
        {
            get
            {
                return this.BranchIDField;
            }
            set
            {
                this.BranchIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string FlexBranchCode
        {
            get
            {
                return this.FlexBranchCodeField;
            }
            set
            {
                this.FlexBranchCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string BranchName
        {
            get
            {
                return this.BranchNameField;
            }
            set
            {
                this.BranchNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string OfficeID
        {
            get
            {
                return this.OfficeIDField;
            }
            set
            {
                this.OfficeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string OfficeName
        {
            get
            {
                return this.OfficeNameField;
            }
            set
            {
                this.OfficeNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string UserName
        {
            get
            {
                return this.UserNameField;
            }
            set
            {
                this.UserNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=10)]
        public int UserTypeID
        {
            get
            {
                return this.UserTypeIDField;
            }
            set
            {
                this.UserTypeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public string UserTypeName
        {
            get
            {
                return this.UserTypeNameField;
            }
            set
            {
                this.UserTypeNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public SSOServiceReference.HubBranches[] SMEsHubBranches
        {
            get
            {
                return this.SMEsHubBranchesField;
            }
            set
            {
                this.SMEsHubBranchesField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="HubBranches", Namespace="http://tempuri.org/")]
    public partial class HubBranches : object
    {
        
        private string BranchIDField;
        
        private string FlexBranchCodeField;
        
        private string BranchNameField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string BranchID
        {
            get
            {
                return this.BranchIDField;
            }
            set
            {
                this.BranchIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string FlexBranchCode
        {
            get
            {
                return this.FlexBranchCodeField;
            }
            set
            {
                this.FlexBranchCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string BranchName
        {
            get
            {
                return this.BranchNameField;
            }
            set
            {
                this.BranchNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BranchInfo", Namespace="http://tempuri.org/")]
    public partial class BranchInfo : object
    {
        
        private string BranchIDField;
        
        private string FlexBranchCodeField;
        
        private string BranchNameField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string BranchID
        {
            get
            {
                return this.BranchIDField;
            }
            set
            {
                this.BranchIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string FlexBranchCode
        {
            get
            {
                return this.FlexBranchCodeField;
            }
            set
            {
                this.FlexBranchCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string BranchName
        {
            get
            {
                return this.BranchNameField;
            }
            set
            {
                this.BranchNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PackageClass", Namespace="http://tempuri.org/")]
    public partial class PackageClass : object
    {
        
        private int PackageIDField;
        
        private string PackageNameField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int PackageID
        {
            get
            {
                return this.PackageIDField;
            }
            set
            {
                this.PackageIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string PackageName
        {
            get
            {
                return this.PackageNameField;
            }
            set
            {
                this.PackageNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="ArrayOfInt", Namespace="http://tempuri.org/", ItemName="int")]
    public class ArrayOfInt : System.Collections.Generic.List<int>
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="User_Application", Namespace="http://tempuri.org/")]
    public partial class User_Application : object
    {
        
        private int ApplicationIDField;
        
        private string ApplicationNameField;
        
        private string ApplicationURlField;
        
        private int GroupIDField;
        
        private string GroupNameField;
        
        private int PackageIDField;
        
        private string PackageNameField;
        
        private int PackageGroupIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int ApplicationID
        {
            get
            {
                return this.ApplicationIDField;
            }
            set
            {
                this.ApplicationIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string ApplicationName
        {
            get
            {
                return this.ApplicationNameField;
            }
            set
            {
                this.ApplicationNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string ApplicationURl
        {
            get
            {
                return this.ApplicationURlField;
            }
            set
            {
                this.ApplicationURlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int GroupID
        {
            get
            {
                return this.GroupIDField;
            }
            set
            {
                this.GroupIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string GroupName
        {
            get
            {
                return this.GroupNameField;
            }
            set
            {
                this.GroupNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int PackageID
        {
            get
            {
                return this.PackageIDField;
            }
            set
            {
                this.PackageIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string PackageName
        {
            get
            {
                return this.PackageNameField;
            }
            set
            {
                this.PackageNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=7)]
        public int PackageGroupID
        {
            get
            {
                return this.PackageGroupIDField;
            }
            set
            {
                this.PackageGroupIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="SSOServiceReference.ServiceSoap")]
    public interface ServiceSoap
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CheckTicket", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.CheckTicketResponse> CheckTicketAsync(SSOServiceReference.CheckTicketRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetUserInfo", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetUserInfoResponse> GetUserInfoAsync(SSOServiceReference.GetUserInfoRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetAll_UserInfo_With_ApplicationID", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetAll_UserInfo_With_ApplicationIDResponse> GetAll_UserInfo_With_ApplicationIDAsync(SSOServiceReference.GetAll_UserInfo_With_ApplicationIDRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetApplicationName", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetApplicationNameResponse> GetApplicationNameAsync(SSOServiceReference.GetApplicationNameRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetGroupName", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetGroupNameResponse> GetGroupNameAsync(SSOServiceReference.GetGroupNameRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetAll_Branches", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetAll_BranchesResponse> GetAll_BranchesAsync(SSOServiceReference.GetAll_BranchesRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetAllPackages", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetAllPackagesResponse> GetAllPackagesAsync(SSOServiceReference.GetAllPackagesRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetSpecificPackages", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetSpecificPackagesResponse> GetSpecificPackagesAsync(SSOServiceReference.GetSpecificPackagesRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetExceptPackagese", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetExceptPackageseResponse> GetExceptPackageseAsync(SSOServiceReference.GetExceptPackageseRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetExceptPackageseByEmp", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetExceptPackageseByEmpResponse> GetExceptPackageseByEmpAsync(SSOServiceReference.GetExceptPackageseByEmpRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetUserPackages", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetUserPackagesResponse> GetUserPackagesAsync(SSOServiceReference.GetUserPackagesRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetPackageName", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetPackageNameResponse> GetPackageNameAsync(SSOServiceReference.GetPackageNameRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetUserApplication", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetUserApplicationResponse> GetUserApplicationAsync(SSOServiceReference.GetUserApplicationRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetUserApplication_Intranet", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetUserApplication_IntranetResponse> GetUserApplication_IntranetAsync(SSOServiceReference.GetUserApplication_IntranetRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetBranchName", ReplyAction="*")]
        System.Threading.Tasks.Task<SSOServiceReference.GetBranchNameResponse> GetBranchNameAsync(SSOServiceReference.GetBranchNameRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CheckTicketRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CheckTicket", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.CheckTicketRequestBody Body;
        
        public CheckTicketRequest()
        {
        }
        
        public CheckTicketRequest(SSOServiceReference.CheckTicketRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CheckTicketRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public decimal TicketID;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public int GroupID;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public int ApplicationID;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public int UID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string UserID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string Key;
        
        public CheckTicketRequestBody()
        {
        }
        
        public CheckTicketRequestBody(decimal TicketID, int GroupID, int ApplicationID, int UID, string UserID, string Key)
        {
            this.TicketID = TicketID;
            this.GroupID = GroupID;
            this.ApplicationID = ApplicationID;
            this.UID = UID;
            this.UserID = UserID;
            this.Key = Key;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CheckTicketResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CheckTicketResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.CheckTicketResponseBody Body;
        
        public CheckTicketResponse()
        {
        }
        
        public CheckTicketResponse(SSOServiceReference.CheckTicketResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CheckTicketResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public bool CheckTicketResult;
        
        public CheckTicketResponseBody()
        {
        }
        
        public CheckTicketResponseBody(bool CheckTicketResult)
        {
            this.CheckTicketResult = CheckTicketResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetUserInfoRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetUserInfo", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetUserInfoRequestBody Body;
        
        public GetUserInfoRequest()
        {
        }
        
        public GetUserInfoRequest(SSOServiceReference.GetUserInfoRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetUserInfoRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string userID;
        
        public GetUserInfoRequestBody()
        {
        }
        
        public GetUserInfoRequestBody(string userID)
        {
            this.userID = userID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetUserInfoResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetUserInfoResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetUserInfoResponseBody Body;
        
        public GetUserInfoResponse()
        {
        }
        
        public GetUserInfoResponse(SSOServiceReference.GetUserInfoResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetUserInfoResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.UserInfo GetUserInfoResult;
        
        public GetUserInfoResponseBody()
        {
        }
        
        public GetUserInfoResponseBody(SSOServiceReference.UserInfo GetUserInfoResult)
        {
            this.GetUserInfoResult = GetUserInfoResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetAll_UserInfo_With_ApplicationIDRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetAll_UserInfo_With_ApplicationID", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetAll_UserInfo_With_ApplicationIDRequestBody Body;
        
        public GetAll_UserInfo_With_ApplicationIDRequest()
        {
        }
        
        public GetAll_UserInfo_With_ApplicationIDRequest(SSOServiceReference.GetAll_UserInfo_With_ApplicationIDRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetAll_UserInfo_With_ApplicationIDRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public int ApplicationID;
        
        public GetAll_UserInfo_With_ApplicationIDRequestBody()
        {
        }
        
        public GetAll_UserInfo_With_ApplicationIDRequestBody(int ApplicationID)
        {
            this.ApplicationID = ApplicationID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetAll_UserInfo_With_ApplicationIDResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetAll_UserInfo_With_ApplicationIDResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetAll_UserInfo_With_ApplicationIDResponseBody Body;
        
        public GetAll_UserInfo_With_ApplicationIDResponse()
        {
        }
        
        public GetAll_UserInfo_With_ApplicationIDResponse(SSOServiceReference.GetAll_UserInfo_With_ApplicationIDResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetAll_UserInfo_With_ApplicationIDResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.UserInfo[] GetAll_UserInfo_With_ApplicationIDResult;
        
        public GetAll_UserInfo_With_ApplicationIDResponseBody()
        {
        }
        
        public GetAll_UserInfo_With_ApplicationIDResponseBody(SSOServiceReference.UserInfo[] GetAll_UserInfo_With_ApplicationIDResult)
        {
            this.GetAll_UserInfo_With_ApplicationIDResult = GetAll_UserInfo_With_ApplicationIDResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetApplicationNameRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetApplicationName", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetApplicationNameRequestBody Body;
        
        public GetApplicationNameRequest()
        {
        }
        
        public GetApplicationNameRequest(SSOServiceReference.GetApplicationNameRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetApplicationNameRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public int ApplicationID;
        
        public GetApplicationNameRequestBody()
        {
        }
        
        public GetApplicationNameRequestBody(int ApplicationID)
        {
            this.ApplicationID = ApplicationID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetApplicationNameResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetApplicationNameResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetApplicationNameResponseBody Body;
        
        public GetApplicationNameResponse()
        {
        }
        
        public GetApplicationNameResponse(SSOServiceReference.GetApplicationNameResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetApplicationNameResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string GetApplicationNameResult;
        
        public GetApplicationNameResponseBody()
        {
        }
        
        public GetApplicationNameResponseBody(string GetApplicationNameResult)
        {
            this.GetApplicationNameResult = GetApplicationNameResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetGroupNameRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetGroupName", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetGroupNameRequestBody Body;
        
        public GetGroupNameRequest()
        {
        }
        
        public GetGroupNameRequest(SSOServiceReference.GetGroupNameRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetGroupNameRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public int GroupID;
        
        public GetGroupNameRequestBody()
        {
        }
        
        public GetGroupNameRequestBody(int GroupID)
        {
            this.GroupID = GroupID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetGroupNameResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetGroupNameResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetGroupNameResponseBody Body;
        
        public GetGroupNameResponse()
        {
        }
        
        public GetGroupNameResponse(SSOServiceReference.GetGroupNameResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetGroupNameResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string GetGroupNameResult;
        
        public GetGroupNameResponseBody()
        {
        }
        
        public GetGroupNameResponseBody(string GetGroupNameResult)
        {
            this.GetGroupNameResult = GetGroupNameResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetAll_BranchesRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetAll_Branches", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetAll_BranchesRequestBody Body;
        
        public GetAll_BranchesRequest()
        {
        }
        
        public GetAll_BranchesRequest(SSOServiceReference.GetAll_BranchesRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute()]
    public partial class GetAll_BranchesRequestBody
    {
        
        public GetAll_BranchesRequestBody()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetAll_BranchesResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetAll_BranchesResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetAll_BranchesResponseBody Body;
        
        public GetAll_BranchesResponse()
        {
        }
        
        public GetAll_BranchesResponse(SSOServiceReference.GetAll_BranchesResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetAll_BranchesResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.BranchInfo[] GetAll_BranchesResult;
        
        public GetAll_BranchesResponseBody()
        {
        }
        
        public GetAll_BranchesResponseBody(SSOServiceReference.BranchInfo[] GetAll_BranchesResult)
        {
            this.GetAll_BranchesResult = GetAll_BranchesResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetAllPackagesRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetAllPackages", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetAllPackagesRequestBody Body;
        
        public GetAllPackagesRequest()
        {
        }
        
        public GetAllPackagesRequest(SSOServiceReference.GetAllPackagesRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute()]
    public partial class GetAllPackagesRequestBody
    {
        
        public GetAllPackagesRequestBody()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetAllPackagesResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetAllPackagesResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetAllPackagesResponseBody Body;
        
        public GetAllPackagesResponse()
        {
        }
        
        public GetAllPackagesResponse(SSOServiceReference.GetAllPackagesResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetAllPackagesResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.PackageClass[] GetAllPackagesResult;
        
        public GetAllPackagesResponseBody()
        {
        }
        
        public GetAllPackagesResponseBody(SSOServiceReference.PackageClass[] GetAllPackagesResult)
        {
            this.GetAllPackagesResult = GetAllPackagesResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetSpecificPackagesRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetSpecificPackages", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetSpecificPackagesRequestBody Body;
        
        public GetSpecificPackagesRequest()
        {
        }
        
        public GetSpecificPackagesRequest(SSOServiceReference.GetSpecificPackagesRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetSpecificPackagesRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.ArrayOfInt packageID;
        
        public GetSpecificPackagesRequestBody()
        {
        }
        
        public GetSpecificPackagesRequestBody(SSOServiceReference.ArrayOfInt packageID)
        {
            this.packageID = packageID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetSpecificPackagesResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetSpecificPackagesResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetSpecificPackagesResponseBody Body;
        
        public GetSpecificPackagesResponse()
        {
        }
        
        public GetSpecificPackagesResponse(SSOServiceReference.GetSpecificPackagesResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetSpecificPackagesResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.PackageClass[] GetSpecificPackagesResult;
        
        public GetSpecificPackagesResponseBody()
        {
        }
        
        public GetSpecificPackagesResponseBody(SSOServiceReference.PackageClass[] GetSpecificPackagesResult)
        {
            this.GetSpecificPackagesResult = GetSpecificPackagesResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetExceptPackageseRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetExceptPackagese", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetExceptPackageseRequestBody Body;
        
        public GetExceptPackageseRequest()
        {
        }
        
        public GetExceptPackageseRequest(SSOServiceReference.GetExceptPackageseRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetExceptPackageseRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.ArrayOfInt packageID;
        
        public GetExceptPackageseRequestBody()
        {
        }
        
        public GetExceptPackageseRequestBody(SSOServiceReference.ArrayOfInt packageID)
        {
            this.packageID = packageID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetExceptPackageseResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetExceptPackageseResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetExceptPackageseResponseBody Body;
        
        public GetExceptPackageseResponse()
        {
        }
        
        public GetExceptPackageseResponse(SSOServiceReference.GetExceptPackageseResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetExceptPackageseResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.PackageClass[] GetExceptPackageseResult;
        
        public GetExceptPackageseResponseBody()
        {
        }
        
        public GetExceptPackageseResponseBody(SSOServiceReference.PackageClass[] GetExceptPackageseResult)
        {
            this.GetExceptPackageseResult = GetExceptPackageseResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetExceptPackageseByEmpRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetExceptPackageseByEmp", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetExceptPackageseByEmpRequestBody Body;
        
        public GetExceptPackageseByEmpRequest()
        {
        }
        
        public GetExceptPackageseByEmpRequest(SSOServiceReference.GetExceptPackageseByEmpRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetExceptPackageseByEmpRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string empNO;
        
        public GetExceptPackageseByEmpRequestBody()
        {
        }
        
        public GetExceptPackageseByEmpRequestBody(string empNO)
        {
            this.empNO = empNO;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetExceptPackageseByEmpResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetExceptPackageseByEmpResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetExceptPackageseByEmpResponseBody Body;
        
        public GetExceptPackageseByEmpResponse()
        {
        }
        
        public GetExceptPackageseByEmpResponse(SSOServiceReference.GetExceptPackageseByEmpResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetExceptPackageseByEmpResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.PackageClass[] GetExceptPackageseByEmpResult;
        
        public GetExceptPackageseByEmpResponseBody()
        {
        }
        
        public GetExceptPackageseByEmpResponseBody(SSOServiceReference.PackageClass[] GetExceptPackageseByEmpResult)
        {
            this.GetExceptPackageseByEmpResult = GetExceptPackageseByEmpResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetUserPackagesRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetUserPackages", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetUserPackagesRequestBody Body;
        
        public GetUserPackagesRequest()
        {
        }
        
        public GetUserPackagesRequest(SSOServiceReference.GetUserPackagesRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetUserPackagesRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string userID;
        
        public GetUserPackagesRequestBody()
        {
        }
        
        public GetUserPackagesRequestBody(string userID)
        {
            this.userID = userID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetUserPackagesResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetUserPackagesResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetUserPackagesResponseBody Body;
        
        public GetUserPackagesResponse()
        {
        }
        
        public GetUserPackagesResponse(SSOServiceReference.GetUserPackagesResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetUserPackagesResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.PackageClass[] GetUserPackagesResult;
        
        public GetUserPackagesResponseBody()
        {
        }
        
        public GetUserPackagesResponseBody(SSOServiceReference.PackageClass[] GetUserPackagesResult)
        {
            this.GetUserPackagesResult = GetUserPackagesResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetPackageNameRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetPackageName", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetPackageNameRequestBody Body;
        
        public GetPackageNameRequest()
        {
        }
        
        public GetPackageNameRequest(SSOServiceReference.GetPackageNameRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetPackageNameRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public int packageID;
        
        public GetPackageNameRequestBody()
        {
        }
        
        public GetPackageNameRequestBody(int packageID)
        {
            this.packageID = packageID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetPackageNameResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetPackageNameResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetPackageNameResponseBody Body;
        
        public GetPackageNameResponse()
        {
        }
        
        public GetPackageNameResponse(SSOServiceReference.GetPackageNameResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetPackageNameResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.PackageClass GetPackageNameResult;
        
        public GetPackageNameResponseBody()
        {
        }
        
        public GetPackageNameResponseBody(SSOServiceReference.PackageClass GetPackageNameResult)
        {
            this.GetPackageNameResult = GetPackageNameResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetUserApplicationRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetUserApplication", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetUserApplicationRequestBody Body;
        
        public GetUserApplicationRequest()
        {
        }
        
        public GetUserApplicationRequest(SSOServiceReference.GetUserApplicationRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetUserApplicationRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string userID;
        
        public GetUserApplicationRequestBody()
        {
        }
        
        public GetUserApplicationRequestBody(string userID)
        {
            this.userID = userID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetUserApplicationResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetUserApplicationResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetUserApplicationResponseBody Body;
        
        public GetUserApplicationResponse()
        {
        }
        
        public GetUserApplicationResponse(SSOServiceReference.GetUserApplicationResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetUserApplicationResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.User_Application[] GetUserApplicationResult;
        
        public GetUserApplicationResponseBody()
        {
        }
        
        public GetUserApplicationResponseBody(SSOServiceReference.User_Application[] GetUserApplicationResult)
        {
            this.GetUserApplicationResult = GetUserApplicationResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetUserApplication_IntranetRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetUserApplication_Intranet", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetUserApplication_IntranetRequestBody Body;
        
        public GetUserApplication_IntranetRequest()
        {
        }
        
        public GetUserApplication_IntranetRequest(SSOServiceReference.GetUserApplication_IntranetRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetUserApplication_IntranetRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string userID;
        
        public GetUserApplication_IntranetRequestBody()
        {
        }
        
        public GetUserApplication_IntranetRequestBody(string userID)
        {
            this.userID = userID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetUserApplication_IntranetResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetUserApplication_IntranetResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetUserApplication_IntranetResponseBody Body;
        
        public GetUserApplication_IntranetResponse()
        {
        }
        
        public GetUserApplication_IntranetResponse(SSOServiceReference.GetUserApplication_IntranetResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetUserApplication_IntranetResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.User_Application[] GetUserApplication_IntranetResult;
        
        public GetUserApplication_IntranetResponseBody()
        {
        }
        
        public GetUserApplication_IntranetResponseBody(SSOServiceReference.User_Application[] GetUserApplication_IntranetResult)
        {
            this.GetUserApplication_IntranetResult = GetUserApplication_IntranetResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetBranchNameRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetBranchName", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetBranchNameRequestBody Body;
        
        public GetBranchNameRequest()
        {
        }
        
        public GetBranchNameRequest(SSOServiceReference.GetBranchNameRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetBranchNameRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string branchIDP;
        
        public GetBranchNameRequestBody()
        {
        }
        
        public GetBranchNameRequestBody(string branchIDP)
        {
            this.branchIDP = branchIDP;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetBranchNameResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetBranchNameResponse", Namespace="http://tempuri.org/", Order=0)]
        public SSOServiceReference.GetBranchNameResponseBody Body;
        
        public GetBranchNameResponse()
        {
        }
        
        public GetBranchNameResponse(SSOServiceReference.GetBranchNameResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetBranchNameResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public SSOServiceReference.BranchInfo GetBranchNameResult;
        
        public GetBranchNameResponseBody()
        {
        }
        
        public GetBranchNameResponseBody(SSOServiceReference.BranchInfo GetBranchNameResult)
        {
            this.GetBranchNameResult = GetBranchNameResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    public interface ServiceSoapChannel : SSOServiceReference.ServiceSoap, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    public partial class ServiceSoapClient : System.ServiceModel.ClientBase<SSOServiceReference.ServiceSoap>, SSOServiceReference.ServiceSoap
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public ServiceSoapClient(EndpointConfiguration endpointConfiguration) : 
                base(ServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), ServiceSoapClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ServiceSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(ServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ServiceSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(ServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ServiceSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.CheckTicketResponse> SSOServiceReference.ServiceSoap.CheckTicketAsync(SSOServiceReference.CheckTicketRequest request)
        {
            return base.Channel.CheckTicketAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.CheckTicketResponse> CheckTicketAsync(decimal TicketID, int GroupID, int ApplicationID, int UID, string UserID, string Key)
        {
            SSOServiceReference.CheckTicketRequest inValue = new SSOServiceReference.CheckTicketRequest();
            inValue.Body = new SSOServiceReference.CheckTicketRequestBody();
            inValue.Body.TicketID = TicketID;
            inValue.Body.GroupID = GroupID;
            inValue.Body.ApplicationID = ApplicationID;
            inValue.Body.UID = UID;
            inValue.Body.UserID = UserID;
            inValue.Body.Key = Key;
            return ((SSOServiceReference.ServiceSoap)(this)).CheckTicketAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetUserInfoResponse> SSOServiceReference.ServiceSoap.GetUserInfoAsync(SSOServiceReference.GetUserInfoRequest request)
        {
            return base.Channel.GetUserInfoAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetUserInfoResponse> GetUserInfoAsync(string userID)
        {
            SSOServiceReference.GetUserInfoRequest inValue = new SSOServiceReference.GetUserInfoRequest();
            inValue.Body = new SSOServiceReference.GetUserInfoRequestBody();
            inValue.Body.userID = userID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetUserInfoAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetAll_UserInfo_With_ApplicationIDResponse> SSOServiceReference.ServiceSoap.GetAll_UserInfo_With_ApplicationIDAsync(SSOServiceReference.GetAll_UserInfo_With_ApplicationIDRequest request)
        {
            return base.Channel.GetAll_UserInfo_With_ApplicationIDAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetAll_UserInfo_With_ApplicationIDResponse> GetAll_UserInfo_With_ApplicationIDAsync(int ApplicationID)
        {
            SSOServiceReference.GetAll_UserInfo_With_ApplicationIDRequest inValue = new SSOServiceReference.GetAll_UserInfo_With_ApplicationIDRequest();
            inValue.Body = new SSOServiceReference.GetAll_UserInfo_With_ApplicationIDRequestBody();
            inValue.Body.ApplicationID = ApplicationID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetAll_UserInfo_With_ApplicationIDAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetApplicationNameResponse> SSOServiceReference.ServiceSoap.GetApplicationNameAsync(SSOServiceReference.GetApplicationNameRequest request)
        {
            return base.Channel.GetApplicationNameAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetApplicationNameResponse> GetApplicationNameAsync(int ApplicationID)
        {
            SSOServiceReference.GetApplicationNameRequest inValue = new SSOServiceReference.GetApplicationNameRequest();
            inValue.Body = new SSOServiceReference.GetApplicationNameRequestBody();
            inValue.Body.ApplicationID = ApplicationID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetApplicationNameAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetGroupNameResponse> SSOServiceReference.ServiceSoap.GetGroupNameAsync(SSOServiceReference.GetGroupNameRequest request)
        {
            return base.Channel.GetGroupNameAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetGroupNameResponse> GetGroupNameAsync(int GroupID)
        {
            SSOServiceReference.GetGroupNameRequest inValue = new SSOServiceReference.GetGroupNameRequest();
            inValue.Body = new SSOServiceReference.GetGroupNameRequestBody();
            inValue.Body.GroupID = GroupID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetGroupNameAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetAll_BranchesResponse> SSOServiceReference.ServiceSoap.GetAll_BranchesAsync(SSOServiceReference.GetAll_BranchesRequest request)
        {
            return base.Channel.GetAll_BranchesAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetAll_BranchesResponse> GetAll_BranchesAsync()
        {
            SSOServiceReference.GetAll_BranchesRequest inValue = new SSOServiceReference.GetAll_BranchesRequest();
            inValue.Body = new SSOServiceReference.GetAll_BranchesRequestBody();
            return ((SSOServiceReference.ServiceSoap)(this)).GetAll_BranchesAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetAllPackagesResponse> SSOServiceReference.ServiceSoap.GetAllPackagesAsync(SSOServiceReference.GetAllPackagesRequest request)
        {
            return base.Channel.GetAllPackagesAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetAllPackagesResponse> GetAllPackagesAsync()
        {
            SSOServiceReference.GetAllPackagesRequest inValue = new SSOServiceReference.GetAllPackagesRequest();
            inValue.Body = new SSOServiceReference.GetAllPackagesRequestBody();
            return ((SSOServiceReference.ServiceSoap)(this)).GetAllPackagesAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetSpecificPackagesResponse> SSOServiceReference.ServiceSoap.GetSpecificPackagesAsync(SSOServiceReference.GetSpecificPackagesRequest request)
        {
            return base.Channel.GetSpecificPackagesAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetSpecificPackagesResponse> GetSpecificPackagesAsync(SSOServiceReference.ArrayOfInt packageID)
        {
            SSOServiceReference.GetSpecificPackagesRequest inValue = new SSOServiceReference.GetSpecificPackagesRequest();
            inValue.Body = new SSOServiceReference.GetSpecificPackagesRequestBody();
            inValue.Body.packageID = packageID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetSpecificPackagesAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetExceptPackageseResponse> SSOServiceReference.ServiceSoap.GetExceptPackageseAsync(SSOServiceReference.GetExceptPackageseRequest request)
        {
            return base.Channel.GetExceptPackageseAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetExceptPackageseResponse> GetExceptPackageseAsync(SSOServiceReference.ArrayOfInt packageID)
        {
            SSOServiceReference.GetExceptPackageseRequest inValue = new SSOServiceReference.GetExceptPackageseRequest();
            inValue.Body = new SSOServiceReference.GetExceptPackageseRequestBody();
            inValue.Body.packageID = packageID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetExceptPackageseAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetExceptPackageseByEmpResponse> SSOServiceReference.ServiceSoap.GetExceptPackageseByEmpAsync(SSOServiceReference.GetExceptPackageseByEmpRequest request)
        {
            return base.Channel.GetExceptPackageseByEmpAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetExceptPackageseByEmpResponse> GetExceptPackageseByEmpAsync(string empNO)
        {
            SSOServiceReference.GetExceptPackageseByEmpRequest inValue = new SSOServiceReference.GetExceptPackageseByEmpRequest();
            inValue.Body = new SSOServiceReference.GetExceptPackageseByEmpRequestBody();
            inValue.Body.empNO = empNO;
            return ((SSOServiceReference.ServiceSoap)(this)).GetExceptPackageseByEmpAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetUserPackagesResponse> SSOServiceReference.ServiceSoap.GetUserPackagesAsync(SSOServiceReference.GetUserPackagesRequest request)
        {
            return base.Channel.GetUserPackagesAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetUserPackagesResponse> GetUserPackagesAsync(string userID)
        {
            SSOServiceReference.GetUserPackagesRequest inValue = new SSOServiceReference.GetUserPackagesRequest();
            inValue.Body = new SSOServiceReference.GetUserPackagesRequestBody();
            inValue.Body.userID = userID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetUserPackagesAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetPackageNameResponse> SSOServiceReference.ServiceSoap.GetPackageNameAsync(SSOServiceReference.GetPackageNameRequest request)
        {
            return base.Channel.GetPackageNameAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetPackageNameResponse> GetPackageNameAsync(int packageID)
        {
            SSOServiceReference.GetPackageNameRequest inValue = new SSOServiceReference.GetPackageNameRequest();
            inValue.Body = new SSOServiceReference.GetPackageNameRequestBody();
            inValue.Body.packageID = packageID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetPackageNameAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetUserApplicationResponse> SSOServiceReference.ServiceSoap.GetUserApplicationAsync(SSOServiceReference.GetUserApplicationRequest request)
        {
            return base.Channel.GetUserApplicationAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetUserApplicationResponse> GetUserApplicationAsync(string userID)
        {
            SSOServiceReference.GetUserApplicationRequest inValue = new SSOServiceReference.GetUserApplicationRequest();
            inValue.Body = new SSOServiceReference.GetUserApplicationRequestBody();
            inValue.Body.userID = userID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetUserApplicationAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetUserApplication_IntranetResponse> SSOServiceReference.ServiceSoap.GetUserApplication_IntranetAsync(SSOServiceReference.GetUserApplication_IntranetRequest request)
        {
            return base.Channel.GetUserApplication_IntranetAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetUserApplication_IntranetResponse> GetUserApplication_IntranetAsync(string userID)
        {
            SSOServiceReference.GetUserApplication_IntranetRequest inValue = new SSOServiceReference.GetUserApplication_IntranetRequest();
            inValue.Body = new SSOServiceReference.GetUserApplication_IntranetRequestBody();
            inValue.Body.userID = userID;
            return ((SSOServiceReference.ServiceSoap)(this)).GetUserApplication_IntranetAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<SSOServiceReference.GetBranchNameResponse> SSOServiceReference.ServiceSoap.GetBranchNameAsync(SSOServiceReference.GetBranchNameRequest request)
        {
            return base.Channel.GetBranchNameAsync(request);
        }
        
        public System.Threading.Tasks.Task<SSOServiceReference.GetBranchNameResponse> GetBranchNameAsync(string branchIDP)
        {
            SSOServiceReference.GetBranchNameRequest inValue = new SSOServiceReference.GetBranchNameRequest();
            inValue.Body = new SSOServiceReference.GetBranchNameRequestBody();
            inValue.Body.branchIDP = branchIDP;
            return ((SSOServiceReference.ServiceSoap)(this)).GetBranchNameAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        #if !NET6_0_OR_GREATER
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        #endif
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.ServiceSoap))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.ServiceSoap12))
            {
                System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
                System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
                textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
                result.Elements.Add(textBindingElement);
                System.ServiceModel.Channels.HttpsTransportBindingElement httpsBindingElement = new System.ServiceModel.Channels.HttpsTransportBindingElement();
                httpsBindingElement.AllowCookies = true;
                httpsBindingElement.MaxBufferSize = int.MaxValue;
                httpsBindingElement.MaxReceivedMessageSize = int.MaxValue;
                result.Elements.Add(httpsBindingElement);
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.ServiceSoap))
            {
                return new System.ServiceModel.EndpointAddress("https://plz-secusit-v01.nbe.ahly.bank/SSOService/Service.asmx");
            }
            if ((endpointConfiguration == EndpointConfiguration.ServiceSoap12))
            {
                return new System.ServiceModel.EndpointAddress("https://plz-secusit-v01.nbe.ahly.bank/SSOService/Service.asmx");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        public enum EndpointConfiguration
        {
            
            ServiceSoap,
            
            ServiceSoap12,
        }
    }
}
