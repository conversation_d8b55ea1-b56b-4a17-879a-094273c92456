﻿using Microsoft.EntityFrameworkCore;
using Historical_Web.Models;
using Historical_Web.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// Configure SSO settings
builder.Services.Configure<SSOSettings>(
    builder.Configuration.GetSection("SSOSetting"));

// ✅ Add session services
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Add database context
builder.Services.AddDbContext<Historical_Web.Data.ApplicationDbContext>(options =>
    options.UseSqlServer(
        builder.Configuration.GetConnectionString("DefaultConnection")));

// Register the database service
builder.Services.AddScoped<Historical_Web.Services.DatabaseService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/TransactionInquiry/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx => 
    {
        const int durationInSeconds = 60 * 60 * 24 * 30;
        ctx.Context.Response.Headers.Append("cache-control", $"public,max-age={durationInSeconds}");
    }

});

app.UseRouting();

app.UseSession(); // ✅ Enable session before authorization

// Add SSO authentication middleware after session but before authorization
app.UseSSOAuthentication();

app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=TransactionInquiry}/{action=Index}/{id?}");

app.Run();
