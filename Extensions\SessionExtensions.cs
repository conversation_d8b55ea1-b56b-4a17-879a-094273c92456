namespace Historical_Web.Extensions
{
    public static class SessionExtensions
    {
        // Session keys for SSO parameters
        public const string ApplicationIdKey = "ApplicationID";
        public const string UserIdKey = "UserID";
        public const string GroupIdKey = "GroupID";

        // Extension methods for easy session access
        public static void SetApplicationId(this ISession session, string applicationId)
        {
            session.SetString(ApplicationIdKey, applicationId ?? string.Empty);
        }

        public static string? GetApplicationId(this ISession session)
        {
            return session.GetString(ApplicationIdKey);
        }

        public static void SetUserId(this ISession session, string userId)
        {
            session.SetString(UserIdKey, userId ?? string.Empty);
        }

        public static string? GetUserId(this ISession session)
        {
            return session.GetString(UserIdKey);
        }

        public static void SetGroupId(this ISession session, string groupId)
        {
            session.SetString(GroupIdKey, groupId ?? string.Empty);
        }

        public static string? GetGroupId(this ISession session)
        {
            return session.GetString(GroupIdKey);
        }

        // Helper method to check if user is in Finders group
        public static bool IsFinderUser(this ISession session, string findersGroupId)
        {
            var userGroupId = session.GetGroupId();
            return !string.IsNullOrEmpty(userGroupId) && userGroupId.Equals(findersGroupId, StringComparison.OrdinalIgnoreCase);
        }

        // Helper method to check if user is business admin
        public static bool IsBusinessAdmin(this ISession session, string businessAdminsGroupId)
        {
            var userGroupId = session.GetGroupId();
            return !string.IsNullOrEmpty(userGroupId) && userGroupId.Equals(businessAdminsGroupId, StringComparison.OrdinalIgnoreCase);
        }
    }
}
