{"GlobalPropertiesHash": "x5ywim1cBX+NFcVY6lw5iX+JUcT37VqxQSQLDm757Wo=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["36jPYEXJKAmLjOb/e8Q5lKchFrCabT14J7EpZp8pIaQ=", "Ni2ce/uGisZHY3usWXqJZkAGSpcd1/5HdH9QnqkVybA=", "tJ1r12VLwqwzIH7PClkLTCiHjjjWL4CcGAazIPrSNrk=", "fdGTpw5yXAON/iJ3BlfkVfWvFgXOfeTkuq1RYx+ucBg=", "HMMTNyJjQkVFuC2JBKf5PRLtZhk3vqb70h8qj0wjKLA=", "4meuT91WFQE+MB4BQlF4liIEUVc0UARu+WeCjBVF1XU=", "jcvoi9TwhNdIP4m7n+TysHqOeOXnoaplTVYDIgyQEVA=", "YH8cZsoQrCSDtYqKahMofu0Iy7JOuccTUvfb4wxH1Vw=", "i7kuiU7eNgJUlfjhtnHftZBnaEQfpyWcLIoL0yxmM8s=", "GaSd4icVfiAF2ae6tvSWAI3RA3uRyZGawO4KLY7szOY=", "RdpNNq3O7Af7yRPB6786NYihCnIut1qW4V6mIrpr4dc=", "EXLw37fF0NPWlT997tOQiBZ0PlZVXnF5NUZsK5Zf6AY=", "G3B8pNIfnxdW1VkeHQK7SyeVwTD+kOiGlaOIW7SJ1oM=", "OKqMqGcDbFJshvWqepkYQx3PREUzS7plUiQBUJ7VWGo=", "e/OzldkaUxljY1eZ+dKNm9Rr2w7iRzlk3BdjbsRTTiU=", "W2Ko1D2jiE14gAU6WtwSJVxpIFb1WfVXuYodRFiilao=", "Is50ccHXbRnFd3VEIiJTifx+YVRbZmVGnbBtktulJbA=", "peZUDfryIeZeKmslWjo+zD7x0kuBnUhTMjIRwxEtOF8=", "0dWnECrpiYnOUZzQEQ8maoHEF58tMVmxr8W/Cwy72C4=", "lsV2oYb+VFyqb7cWcuQnpUqS98UXhTZ18Z40ljcPpL8=", "Gz7oQoPNox7ZSnCk2Jj+no8zU+FN43vXKlWUWMhisaY=", "18ligPkhgERVjWFn/O8cB38vbhW9uWFWEkxA75oY4O4=", "tbe8/snr4z6w9bvnMA/dj8uChVEe3v+kot7UjIovw2A=", "QZhcuufhECjM3TQB4PndbiQZ+f8GHBOFFGFVUfNVrnw=", "7zrKc7P8Jnfhg294ytUCyu0uVHCAZq1q4mxO55nycUU=", "2s3/nkiXAqdY9fHXkiQhUZ1n02y1Ka305/JzUeGSoxQ=", "omo3meKki94TU1A8T3/CM3N5VAkN1ylJt5I30XU+6LM=", "acu7wybAw1mfhphHBew6hMYBUHTfDDMZdkceZFjCA7g=", "RLDCoGC3w9+EJ85kmSmcqlcgQbCfJQkvvZ4QUU+SLmE=", "vjllXS24stHER7ck+JtSJH9rvyx7BNYKUiphmeQq4Y8=", "FxS8NssIKrHVmbdjR8VL8LxZWRX2/41w3MeF/xyfikQ=", "o0m1HiHHCk8HVlaM5CLzHNgHrR8UEVFAht9/ghq0EzA=", "v1ChMGJePNfW5knjIsx5iFoPK00AanNIt4w0qx5R22Q=", "EqWHzplLFyhJUIpMyCL8mkmr0EJqJfhq717hgUT9584=", "kYNgxF9aH+K6wKgUipqzpOHlTJoKXa1Oam05y6Z9FIU=", "0qfBvpri1BNIP41XytyWrcQSPyTV0RY8oK1XxGiPOGs=", "nVVeNEqrNNeMryG/LGHlaphvVClJgLwV/7Udi8qDfqA=", "BLEPiVaINFjMLppIAZdpC8f+tzs/p/ANWdSe6qG9m2w=", "XMrzNZN19pUH90Z9/EOxsA5UZQVStUq7DnqeJbwuxhg=", "i0TouR1EVKOCPk2nle/v1lH8zEoaivkRvceRcfmwkYg=", "i7ErKKVMnD1MJCGFKoL9W+gEVNIxD/0KKBTzAlCIGJg=", "9c8V2CQkuZSCpIygzgSetUKObcYOatvBANLykVgSZ6s=", "xmDWF4T4TIaCbXos8YEegQRwrQeoG3lB401ZMYeVCUQ=", "j6yGIPibgKVtbaRsCSTps8e8Pwr4KF3ILaOrMjho4gU=", "UMhhDreyWeP5RK5ivqDROM+xaAkFzmojIlp+ofHBIoE=", "Ri9zFaQcFtWq884cg9r/HcMPkm9thFVIWijL8bZLkww=", "bvV/PqpaHvlMTHII/O18Dj3D4p3Iz05JdQpDYlfAHpM=", "f0ipxtkbkkaMWUePkSIa7FvvZbX54VjZTiDqkdDFp0s=", "iwe9YVBTBDy7c7O2ueJrFbL4y8zUbbgCW5n9Hwtfhkc=", "Y05sNKHkI6xvtVMQkUYiWuk6pq79WjJ1hk6T5gHiYBM=", "g9UTU/TQr0zIjwtgM+obW0qaAGRibrgPD8Qmluu6Lbw=", "w0r1wXt1gKdBuPTvW+CkvD7tkFwI8xuC+ISbn4dBm8Y=", "mRf/VEN37i3iFhuCoOWVyMNkISexsVgUpVOdvNien8c=", "0n0cfXiqe8GZIzxFVMqjNv5CVO/rcYkkR2t1fRnvC0o=", "JgfJcCy4DdIof8eixbmfcJ3Vx0Joe2qaQjl7H5Qqlo8=", "3kgCUt2j64G+hryb+0V1V660I5ybMaW34UjLProXb/w=", "AgwLb4L8Q7RPZ/PebZLjT4HSA/QqLSH7UhR3FK7CrQ4=", "YfCJSPFHEdeAaAF/3LBVx9dXv/5na0Y7+JBkisn3NfA=", "fn2CqwLKNE6xxqSSnMvBeN+0DCApmcp8/MKx02UsHyk=", "s2kuwOC4jFQSgIAACqWhz/OZ47aMJELwKfeRft/LBMU=", "Qd0DVoBj1pTAXPhOBEHOKHSyq2DKSWK8voaMmt7JggQ=", "c0kWnVTB8Nxwr33OEmp4ovGFyKdBowrL3Rs9ewMXT2E=", "h2jngQz7b7jbx7dfaon2gJZ1/2/RsMumwPDa8PYiazA=", "Zhq2uhEUp/7UigrmcHQzoENFVTXD56APt99znaM9MZg=", "cag8Au033YlMPi4vd6SUyl9JO5eBOIsxy0WBueLhMrY=", "TSRlPYk2l8pW+UfD0PxgOJ2ixc25s6DYU3ju5fjQAeo=", "Ma9UGSpZVR5ts1Q9rppso2IgW37i9DNU5RvhDjPEYN8=", "8eV6wotbL5wc98ucSQE5sDtnZxyLOnVDL3i+ULv4EQ0=", "0KIKtLFsFYbkdXxhqjgTmnZ0Gg0vFp0nkBwgQzYA+ug=", "eidppyf8RbIqxr81Kfs1ZFMLC5srBjChmnE3m+PI0qo=", "DXzEcOifH+U4HjyihzsxAQ5DL5zk7Lrdceuiuq6WaF8=", "V49yMY+KlwOe5OOTZtxtjjIZlUyYY8evh2tL7GzO8FE=", "fCgY0+vcvs39j3T0k7Br/0SnoS01ESoIry/JrpCl5Wk=", "3IRFbwIB06DUgYclIvkC+MBEpLmS87TPoIaPcsxEh74=", "8k0jIUZ7FvbsglL0nUitZLA1/6xm95M7bKLjSZWEZw8=", "j1ey2pA4cj8DLqjPfMz29ZhNqNCQXa6kbCSvmraWw54=", "+ie1yjMXsQq3PDob/rVf0wbZyy+7hh/4PhntKXzAncw=", "K5LMO7gNwMh2IrMZxB3hMpHteDtAqxZOwgHd1IzzyAk=", "JfvDorUHRdUtdWWnXlPQB4VWwjOGa9vZkbqHuJYKKdI="], "CachedAssets": {"0KIKtLFsFYbkdXxhqjgTmnZ0Gg0vFp0nkBwgQzYA+ug=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\select2\\js\\select2.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/select2/js/select2.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z1f3u68ax1", "Integrity": "9yRP/2EFlblE92vzCA10469Ctd0jT48HnmmMw5rJZrA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\js\\select2.min.js", "FileLength": 73163, "LastWriteTime": "2025-05-28T09:25:01.2662016+00:00"}, "Qd0DVoBj1pTAXPhOBEHOKHSyq2DKSWK8voaMmt7JggQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-05-19T11:29:53.9472738+00:00"}, "Ma9UGSpZVR5ts1Q9rppso2IgW37i9DNU5RvhDjPEYN8=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-05-19T11:29:53.9958765+00:00"}, "TSRlPYk2l8pW+UfD0PxgOJ2ixc25s6DYU3ju5fjQAeo=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-05-19T11:29:53.9452731+00:00"}, "cag8Au033YlMPi4vd6SUyl9JO5eBOIsxy0WBueLhMrY=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-05-19T11:29:53.9442731+00:00"}, "Zhq2uhEUp/7UigrmcHQzoENFVTXD56APt99znaM9MZg=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-05-19T11:29:53.9432732+00:00"}, "h2jngQz7b7jbx7dfaon2gJZ1/2/RsMumwPDa8PYiazA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-05-19T11:29:54.0903807+00:00"}, "c0kWnVTB8Nxwr33OEmp4ovGFyKdBowrL3Rs9ewMXT2E=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-05-19T11:29:53.9472738+00:00"}, "s2kuwOC4jFQSgIAACqWhz/OZ47aMJELwKfeRft/LBMU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-05-19T11:29:53.9462733+00:00"}, "8eV6wotbL5wc98ucSQE5sDtnZxyLOnVDL3i+ULv4EQ0=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\select2\\css\\select2.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/select2/css/select2.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "66wye7e524", "Integrity": "XkFWKlPlxi9E2fiymqJAfajRHhFVdCdCr3BJfZ0jkNs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\css\\select2.min.css", "FileLength": 9632, "LastWriteTime": "2025-05-28T09:17:55.7695335+00:00"}, "fn2CqwLKNE6xxqSSnMvBeN+0DCApmcp8/MKx02UsHyk=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-05-19T11:29:53.9462733+00:00"}, "YfCJSPFHEdeAaAF/3LBVx9dXv/5na0Y7+JBkisn3NfA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-05-19T11:29:54.0923801+00:00"}, "AgwLb4L8Q7RPZ/PebZLjT4HSA/QqLSH7UhR3FK7CrQ4=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-05-19T11:29:54.0913804+00:00"}, "3kgCUt2j64G+hryb+0V1V660I5ybMaW34UjLProXb/w=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-05-19T11:29:54.0913804+00:00"}, "JgfJcCy4DdIof8eixbmfcJ3Vx0Joe2qaQjl7H5Qqlo8=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-05-19T11:29:53.9948699+00:00"}, "0n0cfXiqe8GZIzxFVMqjNv5CVO/rcYkkR2t1fRnvC0o=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-05-19T11:29:53.9968759+00:00"}, "mRf/VEN37i3iFhuCoOWVyMNkISexsVgUpVOdvNien8c=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-05-19T11:29:53.9958765+00:00"}, "w0r1wXt1gKdBuPTvW+CkvD7tkFwI8xuC+ISbn4dBm8Y=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-05-19T11:29:53.9918899+00:00"}, "g9UTU/TQr0zIjwtgM+obW0qaAGRibrgPD8Qmluu6Lbw=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-05-19T11:29:53.9898904+00:00"}, "Y05sNKHkI6xvtVMQkUYiWuk6pq79WjJ1hk6T5gHiYBM=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-05-19T11:29:53.9898904+00:00"}, "iwe9YVBTBDy7c7O2ueJrFbL4y8zUbbgCW5n9Hwtfhkc=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-05-19T11:29:53.9852051+00:00"}, "f0ipxtkbkkaMWUePkSIa7FvvZbX54VjZTiDqkdDFp0s=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-05-19T11:29:53.9852051+00:00"}, "bvV/PqpaHvlMTHII/O18Dj3D4p3Iz05JdQpDYlfAHpM=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-05-19T11:29:53.9834272+00:00"}, "Ri9zFaQcFtWq884cg9r/HcMPkm9thFVIWijL8bZLkww=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-05-19T11:29:53.982427+00:00"}, "UMhhDreyWeP5RK5ivqDROM+xaAkFzmojIlp+ofHBIoE=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-05-19T11:29:53.9814273+00:00"}, "j6yGIPibgKVtbaRsCSTps8e8Pwr4KF3ILaOrMjho4gU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-05-19T11:29:53.9804272+00:00"}, "xmDWF4T4TIaCbXos8YEegQRwrQeoG3lB401ZMYeVCUQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-05-19T11:29:53.9784276+00:00"}, "9c8V2CQkuZSCpIygzgSetUKObcYOatvBANLykVgSZ6s=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-05-19T11:29:53.9774272+00:00"}, "i7ErKKVMnD1MJCGFKoL9W+gEVNIxD/0KKBTzAlCIGJg=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-05-19T11:29:53.9764271+00:00"}, "i0TouR1EVKOCPk2nle/v1lH8zEoaivkRvceRcfmwkYg=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-05-19T11:29:53.9754276+00:00"}, "XMrzNZN19pUH90Z9/EOxsA5UZQVStUq7DnqeJbwuxhg=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-05-19T11:29:53.9734286+00:00"}, "BLEPiVaINFjMLppIAZdpC8f+tzs/p/ANWdSe6qG9m2w=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-05-19T11:29:53.9724288+00:00"}, "nVVeNEqrNNeMryG/LGHlaphvVClJgLwV/7Udi8qDfqA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-05-19T11:29:53.9714289+00:00"}, "0qfBvpri1BNIP41XytyWrcQSPyTV0RY8oK1XxGiPOGs=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-05-19T11:29:53.9704323+00:00"}, "kYNgxF9aH+K6wKgUipqzpOHlTJoKXa1Oam05y6Z9FIU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-05-19T11:29:53.9684301+00:00"}, "EqWHzplLFyhJUIpMyCL8mkmr0EJqJfhq717hgUT9584=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-05-19T11:29:53.9673025+00:00"}, "v1ChMGJePNfW5knjIsx5iFoPK00AanNIt4w0qx5R22Q=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-05-19T11:29:53.9667855+00:00"}, "o0m1HiHHCk8HVlaM5CLzHNgHrR8UEVFAht9/ghq0EzA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-05-19T11:29:53.9657859+00:00"}, "FxS8NssIKrHVmbdjR8VL8LxZWRX2/41w3MeF/xyfikQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-05-19T11:29:53.9647859+00:00"}, "vjllXS24stHER7ck+JtSJH9rvyx7BNYKUiphmeQq4Y8=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-05-19T11:29:53.9647859+00:00"}, "RLDCoGC3w9+EJ85kmSmcqlcgQbCfJQkvvZ4QUU+SLmE=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-05-19T11:29:53.9647859+00:00"}, "acu7wybAw1mfhphHBew6hMYBUHTfDDMZdkceZFjCA7g=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-05-19T11:29:53.9637856+00:00"}, "omo3meKki94TU1A8T3/CM3N5VAkN1ylJt5I30XU+6LM=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-05-19T11:29:53.9627867+00:00"}, "2s3/nkiXAqdY9fHXkiQhUZ1n02y1Ka305/JzUeGSoxQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-05-19T11:29:53.9627867+00:00"}, "7zrKc7P8Jnfhg294ytUCyu0uVHCAZq1q4mxO55nycUU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-05-19T11:29:53.9617859+00:00"}, "QZhcuufhECjM3TQB4PndbiQZ+f8GHBOFFGFVUfNVrnw=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-05-19T11:29:53.9617859+00:00"}, "tbe8/snr4z6w9bvnMA/dj8uChVEe3v+kot7UjIovw2A=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-05-19T11:29:53.9617859+00:00"}, "18ligPkhgERVjWFn/O8cB38vbhW9uWFWEkxA75oY4O4=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-05-19T11:29:53.9607858+00:00"}, "Gz7oQoPNox7ZSnCk2Jj+no8zU+FN43vXKlWUWMhisaY=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-05-19T11:29:53.9607858+00:00"}, "lsV2oYb+VFyqb7cWcuQnpUqS98UXhTZ18Z40ljcPpL8=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-05-19T11:29:53.9607858+00:00"}, "0dWnECrpiYnOUZzQEQ8maoHEF58tMVmxr8W/Cwy72C4=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-05-19T11:29:53.959786+00:00"}, "peZUDfryIeZeKmslWjo+zD7x0kuBnUhTMjIRwxEtOF8=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-05-19T11:29:53.959786+00:00"}, "Is50ccHXbRnFd3VEIiJTifx+YVRbZmVGnbBtktulJbA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-05-19T11:29:53.9587866+00:00"}, "W2Ko1D2jiE14gAU6WtwSJVxpIFb1WfVXuYodRFiilao=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-05-19T11:29:53.9587866+00:00"}, "e/OzldkaUxljY1eZ+dKNm9Rr2w7iRzlk3BdjbsRTTiU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-05-19T11:29:53.9517842+00:00"}, "OKqMqGcDbFJshvWqepkYQx3PREUzS7plUiQBUJ7VWGo=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-05-19T11:29:53.9506445+00:00"}, "G3B8pNIfnxdW1VkeHQK7SyeVwTD+kOiGlaOIW7SJ1oM=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-05-19T11:29:53.9502756+00:00"}, "EXLw37fF0NPWlT997tOQiBZ0PlZVXnF5NUZsK5Zf6AY=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-05-19T11:29:53.9492749+00:00"}, "RdpNNq3O7Af7yRPB6786NYihCnIut1qW4V6mIrpr4dc=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-05-19T11:29:53.9482875+00:00"}, "GaSd4icVfiAF2ae6tvSWAI3RA3uRyZGawO4KLY7szOY=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\js\\viewBuilder.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "js/viewBuilder#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u0an1wwvio", "Integrity": "fnP8Z2qlwe5nL33d5JukbTAJEIGyP84467QSOo9xPSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\viewBuilder.js", "FileLength": 18542, "LastWriteTime": "2025-06-19T06:59:42.7952661+00:00"}, "i7kuiU7eNgJUlfjhtnHftZBnaEQfpyWcLIoL0yxmM8s=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\js\\transactionInquiry.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "js/transactionInquiry#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0ti6v2nhu4", "Integrity": "tBuPdBUGvUAQ+UTJK7DT592GqIF+MM9jJswwseY4itI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\transactionInquiry.js", "FileLength": 59419, "LastWriteTime": "2025-06-19T07:22:19.9314881+00:00"}, "YH8cZsoQrCSDtYqKahMofu0Iy7JOuccTUvfb4wxH1Vw=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\js\\site.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3r8b2a0bt1", "Integrity": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 3, "LastWriteTime": "2025-06-19T06:59:42.7407789+00:00"}, "jcvoi9TwhNdIP4m7n+TysHqOeOXnoaplTVYDIgyQEVA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\favicon.ico", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-05-19T11:29:54.0903807+00:00"}, "4meuT91WFQE+MB4BQlF4liIEUVc0UARu+WeCjBVF1XU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\view-builder.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/view-builder#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3tjp6gaf54", "Integrity": "eWlozSXE1J631rCRgficVRvqaHRns2d8rSvfbY8qXwM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\view-builder.css", "FileLength": 12898, "LastWriteTime": "2025-05-28T09:15:36.246787+00:00"}, "HMMTNyJjQkVFuC2JBKf5PRLtZhk3vqb70h8qj0wjKLA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\transaction-inquiry.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/transaction-inquiry#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7f4923<PERSON><PERSON><PERSON>", "Integrity": "o3z1SxCKyE0ltdljDbf5cZe4DQAWoiwsakSnj/RD+f4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\transaction-inquiry.css", "FileLength": 40141, "LastWriteTime": "2025-05-28T09:14:17.922513+00:00"}, "fdGTpw5yXAON/iJ3BlfkVfWvFgXOfeTkuq1RYx+ucBg=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\layout.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/layout#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "foco3ldz1j", "Integrity": "0cKevDKmrcPzhEKPdiec6ncMkOV4FQTWQ1XX0G3rnUY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\layout.css", "FileLength": 7356, "LastWriteTime": "2025-05-28T10:38:36.357744+00:00"}, "tJ1r12VLwqwzIH7PClkLTCiHjjjWL4CcGAazIPrSNrk=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\error.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/error#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cnwu351nc5", "Integrity": "yOfuHwhICRdnOyGqmoI57yZ9VCBNQqFRTU45dUg7wnQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\error.css", "FileLength": 8995, "LastWriteTime": "2025-05-28T07:04:34.5328873+00:00"}, "Ni2ce/uGisZHY3usWXqJZkAGSpcd1/5HdH9QnqkVybA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\components.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/components#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lpo996vjsj", "Integrity": "p+noFyjm7wzlQNaGvZ1XGawg4onb/dLrTZr2R/vh4Jg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\components.css", "FileLength": 25803, "LastWriteTime": "2025-06-19T07:22:37.4973368+00:00"}, "36jPYEXJKAmLjOb/e8Q5lKchFrCabT14J7EpZp8pIaQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\base.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/base#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pif4ri5ynq", "Integrity": "kXrVc/7UVRp9jWvd9C2EzQ5g4hZWll76jxaXh/QZv48=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\base.css", "FileLength": 8423, "LastWriteTime": "2025-05-28T06:59:40.3003223+00:00"}}, "CachedCopyCandidates": {}}