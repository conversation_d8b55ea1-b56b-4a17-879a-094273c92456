using Historical_Web.Extensions;
using Historical_Web.Models;
using Microsoft.Extensions.Options;

namespace Historical_Web.Middleware
{
    public class SSOAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly SSOSettings _ssoSettings;
        private readonly ILogger<SSOAuthenticationMiddleware> _logger;

        public SSOAuthenticationMiddleware(
            RequestDelegate next,
            IOptions<SSOSettings> ssoSettings,
            ILogger<SSOAuthenticationMiddleware> logger)
        {
            _next = next;
            _ssoSettings = ssoSettings.Value;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // Extract URL parameters
                var applicationId = context.Request.Query["ApplicationID"].FirstOrDefault();
                var userId = context.Request.Query["UserID"].FirstOrDefault();
                var groupId = context.Request.Query["GrouplD"].FirstOrDefault();

                // If any SSO parameters are present, process them
                if (!string.IsNullOrEmpty(applicationId) || !string.IsNullOrEmpty(userId) || !string.IsNullOrEmpty(groupId))
                {
                    _logger.LogInformation("SSO parameters detected - ApplicationID: {ApplicationId}, UserID: {UserId}, GroupID: {GroupId}",
                        applicationId, userId, groupId);

                    // Validate Application ID - it must be provided and match the configured value
                    if (string.IsNullOrEmpty(applicationId))
                    {
                        _logger.LogWarning("Application ID is missing but other SSO parameters are present");
                        context.Response.Redirect(_ssoSettings.LoginURL);
                        return;
                    }

                    if (!applicationId.Equals(_ssoSettings.ApplicationID, StringComparison.OrdinalIgnoreCase))
                    {
                        _logger.LogWarning("Invalid Application ID received: {ApplicationId}, expected: {ExpectedApplicationId}",
                            applicationId, _ssoSettings.ApplicationID);

                        // Redirect to login URL
                        context.Response.Redirect(_ssoSettings.LoginURL);
                        return;
                    }

                    // Store parameters in session
                    // ApplicationID is guaranteed to be valid at this point
                    context.Session.SetApplicationId(applicationId);

                    if (!string.IsNullOrEmpty(userId))
                    {
                        context.Session.SetUserId(userId);
                    }

                    if (!string.IsNullOrEmpty(groupId))
                    {
                        context.Session.SetGroupId(groupId);
                    }

                    _logger.LogInformation("SSO parameters stored in session successfully");
                }

                // Continue to next middleware
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SSO Authentication Middleware");
                
                // Continue to next middleware even if there's an error
                // to avoid breaking the application
                await _next(context);
            }
        }
    }

    // Extension method to register the middleware
    public static class SSOAuthenticationMiddlewareExtensions
    {
        public static IApplicationBuilder UseSSOAuthentication(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<SSOAuthenticationMiddleware>();
        }
    }
}
