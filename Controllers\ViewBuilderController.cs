using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Historical_Web.Data;
using Historical_Web.Models;
using Historical_Web.Services;
using Newtonsoft.Json;

namespace Historical_Web.Controllers
{
    public class ViewBuilderController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly DatabaseService _databaseService;
        private readonly ILogger<ViewBuilderController> _logger;

        public ViewBuilderController(
            ApplicationDbContext context,
            DatabaseService databaseService,
            ILogger<ViewBuilderController> logger)
        {
            _context = context;
            _databaseService = databaseService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var viewModel = new ViewBuilderViewModel
                {
                    AvailableTables = await _databaseService.GetTableNamesAsync(),
                    ExistingViews = await _context.GridViews.OrderBy(v => v.ViewName).ToListAsync()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading ViewBuilder page");
                return View("Error", new ErrorViewModel { RequestId = HttpContext.TraceIdentifier });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTableColumns(string tableName)
        {
            try
            {
                if (string.IsNullOrEmpty(tableName))
                    return BadRequest("Table name is required");

                var columns = await _databaseService.GetTableColumnsAsync(tableName);
                return Json(columns);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting table columns for {TableName}", tableName);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetView(int id)
        {
            try
            {
                var view = await _context.GridViews.FindAsync(id);
                if (view == null)
                    return NotFound();

                var columnMappings = new List<ColumnMapping>();
                if (!string.IsNullOrEmpty(view.ColumnMappings))
                {
                    var mappings = JsonConvert.DeserializeObject<Dictionary<string, string>>(view.ColumnMappings);
                    if (mappings != null)
                    {
                        int order = 0;
                        foreach (var mapping in mappings)
                        {
                            columnMappings.Add(new ColumnMapping
                            {
                                ColumnName = mapping.Key,
                                Alias = mapping.Value,
                                Order = order++
                            });
                        }
                    }
                }

                var result = new
                {
                    viewID = view.ViewID,
                    viewName = view.ViewName,
                    tableName = view.TableName,
                    columnMappings = columnMappings
                };

                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting view {ViewId}", id);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveView([FromBody] SaveViewRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ViewName) || string.IsNullOrEmpty(request.TableName))
                    return BadRequest("View name and table name are required");

                if (request.ColumnMappings == null || request.ColumnMappings.Count == 0)
                    return BadRequest("At least one column mapping is required");

                // Convert column mappings to JSON
                var mappingsDict = request.ColumnMappings.ToDictionary(
                    cm => cm.ColumnName,
                    cm => cm.Alias
                );
                var columnMappingsJson = JsonConvert.SerializeObject(mappingsDict);

                GridView? view;
                if (request.ViewID.HasValue && request.ViewID.Value > 0)
                {
                    // Update existing view
                    view = await _context.GridViews.FindAsync(request.ViewID.Value);
                    if (view == null)
                        return NotFound("View not found");

                    view.ViewName = request.ViewName;
                    view.TableName = request.TableName;
                    view.ColumnMappings = columnMappingsJson;
                }
                else
                {
                    // Create new view
                    view = new GridView
                    {
                        ViewName = request.ViewName,
                        TableName = request.TableName,
                        ColumnMappings = columnMappingsJson
                    };
                    _context.GridViews.Add(view);
                }

                await _context.SaveChangesAsync();

                return Json(new { success = true, viewId = view.ViewID, message = "View saved successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving view");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteView(int id)
        {
            try
            {
                var view = await _context.GridViews.FindAsync(id);
                if (view == null)
                    return NotFound();

                _context.GridViews.Remove(view);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "View deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting view {ViewId}", id);
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }

    public class SaveViewRequest
    {
        public int? ViewID { get; set; }
        public string ViewName { get; set; } = string.Empty;
        public string TableName { get; set; } = string.Empty;
        public List<ColumnMapping> ColumnMappings { get; set; } = new List<ColumnMapping>();
    }
}
