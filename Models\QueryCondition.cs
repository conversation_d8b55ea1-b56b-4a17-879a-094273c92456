namespace Historical_Web.Models
{
    public class QueryCondition
    {
        public string Column { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string LogicalOperator { get; set; } = "AND";
        public bool IsDateColumn { get; set; } = false;
        public bool IsBetween { get; set; } = false;

        public string ToSqlString()
        {
            // Handle BETWEEN operator for dates
            if (IsDateColumn && Operator == "BETWEEN" && IsBetween && !string.IsNullOrEmpty(Value))
            {
                // Split the value into start and end dates
                var dateParts = Value.Split('|');
                if (dateParts.Length == 2)
                {
                    string startDate = dateParts[0];
                    string endDate = dateParts[1];

                    // Try to parse the dates
                    if (DateTime.TryParse(startDate, out DateTime startDateValue) &&
                        DateTime.TryParse(endDate, out DateTime endDateValue))
                    {
                        // Format dates for SQL Server (yyyy-MM-dd)
                        string formattedStartDate = "'" + startDateValue.ToString("yyyy-MM-dd") + "'";
                        string formattedEndDate = "'" + endDateValue.ToString("yyyy-MM-dd") + "'";

                        // Return the BETWEEN clause
                        return $"{Column} BETWEEN {formattedStartDate} AND {formattedEndDate}";
                    }
                }

                // If we can't parse the dates, fall back to treating as a string
                return $"{Column} = '{Value}'";
            }

            string formattedValue = Value;

            // Handle date values
            if (IsDateColumn && !string.IsNullOrEmpty(Value))
            {
                // Try to parse the date
                if (DateTime.TryParse(Value, out DateTime dateValue))
                {
                    // Format date for SQL Server (yyyy-MM-dd)
                    formattedValue = "'" + dateValue.ToString("yyyy-MM-dd") + "'";
                }
                else
                {
                    // If not a valid date, treat as a string
                    formattedValue = $"'{Value}'";
                }
            }
            // Handle IN operator
            else if (Operator == "IN")
            {
                var values = Value.Split(',')
                    .Select(v => v.Trim())
                    .Where(v => !string.IsNullOrEmpty(v))
                    .Select(v => $"'{v}'");

                formattedValue = $"({string.Join(", ", values)})";
            }
            // Handle string values by adding quotes
            else if (!string.IsNullOrEmpty(Value) )
            {
                formattedValue = $"'{Value}'";
            }

            return $"{Column} {Operator} {formattedValue}";
        }
    }
}
