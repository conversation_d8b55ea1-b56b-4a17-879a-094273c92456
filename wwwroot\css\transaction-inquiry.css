/* ==========================================================================
   TRANSACTION INQUIRY PAGE STYLES
   ========================================================================== */

/* ==========================================================================
   PAGE LAYOUT
   ========================================================================== */

.transaction-inquiry-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
}

/* ==========================================================================
   QUERY BUILDER SECTION
   ========================================================================== */

.query-builder-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.query-builder-body {
    padding: var(--space-8);
}

/* ==========================================================================
   VIEW & TABLE SELECTION
   ========================================================================== */

.selection-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.selection-field {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.field-label {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.field-label .required {
    color: var(--error);
    font-weight: var(--font-bold);
}

.field-label .optional {
    color: var(--text-tertiary);
    font-weight: var(--font-normal);
    font-size: var(--text-sm);
}

.field-wrapper {
    position: relative;
}

.field-select {
    width: 100%;
    padding: var(--space-4) var(--space-5);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-xl);
    font-size: var(--text-base);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.field-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 4px rgba(168, 85, 247, 0.1);
    transform: translateY(-2px);
}

.field-help {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    margin-top: var(--space-2);
}

/* ==========================================================================
   QUERY CONDITIONS
   ========================================================================== */

.conditions-section {
    margin-bottom: var(--space-8);
}

.conditions-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-6);
}

.conditions-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.conditions-container {
    background: var(--gray-50);
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    min-height: 200px;
    position: relative;
}

.conditions-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

/* Individual Condition Card */
.condition-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
}

.condition-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.condition-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: var(--space-4);
    align-items: end;
}

.condition-field {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.condition-label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
}

.condition-input,
.condition-select {
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    background: var(--bg-primary);
    transition: all var(--transition-fast);
}

.condition-input:focus,
.condition-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
}

.condition-remove {
    background: var(--error);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-2);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
}

.condition-remove:hover {
    background: #dc2626;
    transform: scale(1.05);
}

/* Empty State */
.empty-conditions {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--space-8);
    color: var(--text-tertiary);
}

.empty-conditions h6 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-secondary);
    margin-bottom: var(--space-2);
}

.empty-conditions p {
    font-size: var(--text-base);
    margin-bottom: var(--space-4);
}

.empty-conditions .help-text {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
}

/* Add Condition Button */
.add-condition-btn {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    box-shadow: var(--shadow-md);
    margin-top: var(--space-4);
}

.add-condition-btn:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.add-condition-btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* ==========================================================================
   QUERY EXECUTION
   ========================================================================== */

.query-actions {
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
    padding: var(--space-6) var(--space-8);
    border-top: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.execute-query-btn {
    background: linear-gradient(135deg, var(--success), #059669);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    box-shadow: var(--shadow-lg);
}

.execute-query-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.execute-query-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.query-tip {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--text-tertiary);
    font-size: var(--text-sm);
}

/* ==========================================================================
   RESULTS SECTION
   ========================================================================== */

.results-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-top: var(--space-8);
}

/* Results Table Container */
.results-table-container {
    overflow-x: auto;
    max-height: 650px;
    border-radius: var(--radius-xl);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    background: var(--bg-primary);
}

.results-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: var(--text-sm);
    background: var(--bg-primary);
}

.results-table th {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: var(--space-5) var(--space-6);
    text-align: left;
    font-weight: var(--font-bold);
    color: #334155;
    border-bottom: 3px solid #cbd5e1;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    white-space: nowrap;
    font-size: var(--text-sm);
    letter-spacing: 0.025em;
    text-transform: uppercase;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.results-table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid #f1f5f9;
    color: var(--text-primary);
    vertical-align: middle;
    text-align: left;
    white-space: nowrap;
    font-size: var(--text-sm);
    line-height: 1.5;
    transition: all var(--transition-fast);
}

.results-table tbody tr {
    transition: all var(--transition-fast);
    background: var(--bg-primary);
}

.results-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.results-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.results-table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
}

.results-table tbody tr:last-child td {
    border-bottom: none;
}

/* Sticky Actions Column */
.results-table th.actions-column,
.results-table td.actions-column {
    position: sticky;
    right: 0;
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border-left: 3px solid #e2e8f0;
    z-index: var(--z-sticky);
    min-width: 140px;
    box-shadow: -4px 0 8px rgba(0, 0, 0, 0.05);
}

.results-table th.actions-column {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-left-color: #cbd5e1;
}

.results-table tbody tr:hover td.actions-column {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    box-shadow: -4px 0 12px rgba(0, 0, 0, 0.08);
}

/* Results Footer */
.results-footer {
    padding: var(--space-6) var(--space-8);
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-top: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.results-info {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.pagination-info,
.selection-info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    padding: var(--space-2) var(--space-3);
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--radius-lg);
    border: 1px solid #e2e8f0;
}

.selection-info {
    color: var(--primary-700);
    background: rgba(168, 85, 247, 0.1);
    border-color: var(--primary-200);
}

.move-to-active-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    letter-spacing: 0.025em;
}

.move-to-active-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
}

.move-to-active-btn:active:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.move-to-active-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ==========================================================================
   FILTER POPUP STYLES
   ========================================================================== */

.filter-popup {
    position: absolute;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    min-width: 300px;
    max-width: 400px;
    max-height: 500px;
    z-index: var(--z-dropdown);
    overflow: hidden;
}

.filter-popup-header {
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.filter-popup-title {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.filter-popup-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.filter-popup-close:hover {
    background: var(--gray-100);
    color: var(--text-primary);
}

.filter-popup-body {
    padding: var(--space-4);
    max-height: 350px;
    overflow-y: auto;
}

.filter-controls-section {
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-light);
}

.filter-bulk-actions {
    display: flex;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.filter-bulk-btn {
    background: var(--gray-100);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-bulk-btn:hover {
    background: var(--primary-50);
    color: var(--primary-600);
    border-color: var(--primary-300);
}

.filter-count-info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.filter-values-section {
    max-height: 250px;
    overflow-y: auto;
}

.filter-values-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.filter-value-item {
    display: flex;
    align-items: center;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-value-item:hover {
    background: var(--primary-50);
}

.filter-value-checkbox {
    margin-right: var(--space-3);
}

.filter-value-label {
    flex: 1;
    font-size: var(--text-sm);
    color: var(--text-primary);
    cursor: pointer;
}

.filter-value-count {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    background: var(--gray-100);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    min-width: 24px;
    text-align: center;
}

.filter-popup-footer {
    background: var(--gray-50);
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.filter-footer-info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.filter-footer-actions {
    display: flex;
    gap: var(--space-2);
}

.filter-btn {
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.filter-btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: var(--text-inverse);
    border: none;
}

.filter-btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
}

.filter-btn-secondary {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
}

.filter-btn-secondary:hover {
    background: var(--gray-50);
    color: var(--text-primary);
    border-color: var(--border-dark);
}

.filter-loading-state,
.filter-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-8);
    text-align: center;
    color: var(--text-tertiary);
}

.filter-loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-4);
}

.filter-empty-icon {
    font-size: var(--text-3xl);
    margin-bottom: var(--space-3);
    color: var(--text-tertiary);
}

.filter-empty-text {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* ==========================================================================
   TRANSACTION DETAILS MODAL
   ========================================================================== */

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: var(--z-modal-backdrop);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
}

.modal {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.modal-dialog {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}

.modal-title-section {
    flex: 1;
}

.modal-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.modal-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--text-xl);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    line-height: 1;
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--text-primary);
    transform: scale(1.1);
}

.modal-body {
    padding: 0;
    max-height: 60vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.tabs-content {
    flex: 1;
    overflow-y: auto;
}

.tab-content-area {
    padding: var(--space-6);
    min-height: 400px;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-12);
    text-align: center;
    color: var(--text-secondary);
}

.loading-state .spinner {
    margin-bottom: var(--space-4);
}

.loading-state p {
    font-size: var(--text-base);
    color: var(--text-secondary);
}

/* Transaction History Styles */
.timeline-header {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 2px solid var(--border-light);
}

.timeline-header h5 {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.timeline-header .text-muted {
    color: var(--text-tertiary);
    font-weight: var(--font-normal);
}

.card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--space-4);
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.card-header .fw-semibold {
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.card-header .small {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

.card-body {
    padding: var(--space-4);
}

.border-bottom {
    border-bottom: 1px solid var(--border-light) !important;
    padding-bottom: var(--space-3);
    margin-bottom: var(--space-3);
}

.border-bottom:last-child {
    border-bottom: none !important;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Field Change Styling */
.d-flex.justify-content-between {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-3);
}

.d-flex.justify-content-between strong {
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.row {
    display: flex;
    gap: var(--space-4);
    align-items: center;
}

.col-5 {
    flex: 1;
}

.col-2 {
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--primary-600);
}

.text-muted.small {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    margin-bottom: var(--space-1);
}

.border.rounded.p-2.bg-light {
    border: 1px solid var(--border-light) !important;
    border-radius: var(--radius-md) !important;
    padding: var(--space-3) !important;
    background: var(--gray-50) !important;
    font-size: var(--text-sm);
    color: var(--text-secondary);
    min-height: 2.5rem;
    display: flex;
    align-items: center;
}

/* Source Information Styles */
.bg-light.border.rounded.p-3 {
    background: var(--primary-50) !important;
    border: 1px solid var(--primary-200) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-4) !important;
}

.bg-light.border.rounded.p-3 div {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
    font-weight: var(--font-medium);
    color: var(--primary-700);
}

.bg-light.border.rounded.p-3 div:last-child {
    margin-bottom: 0;
}

/* Empty State Styling */
.text-center.text-muted.py-4 {
    text-align: center;
    color: var(--text-tertiary);
    padding: var(--space-12) var(--space-6);
}

.text-center.text-muted.py-4 .display-6 {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-4);
    color: var(--text-tertiary);
}

.text-center.text-muted.py-4 p {
    font-size: var(--text-base);
    color: var(--text-secondary);
    margin-bottom: var(--space-2);
}

.text-center.text-muted.py-4 small {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
}

/* ==========================================================================
   MODAL ANIMATIONS & STATES
   ========================================================================== */

.modal-backdrop.show {
    opacity: 1;
}

.modal-backdrop.show .modal-dialog {
    transform: scale(1) translateY(0);
}

body.modal-open {
    overflow: hidden;
}

/* Active tab styling */
.tabs-button[aria-selected="true"] {
    background: var(--primary-100);
    color: var(--primary-700);
    border-bottom: 2px solid var(--primary-600);
}

/* Improved loading spinner in modal */
.loading-state .spinner {
    width: 2.5rem;
    height: 2.5rem;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ==========================================================================
   SELECT2 ENHANCED DROPDOWN STYLING
   ========================================================================== */

/* Select2 Container */
.select2-container {
    width: 100% !important;
}

.select2-container--default .select2-selection--single {
    height: auto !important;
    padding: var(--space-4) var(--space-5);
    border: 2px solid var(--border-light) !important;
    border-radius: var(--radius-xl) !important;
    font-size: var(--text-base);
    background: var(--bg-primary) !important;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    min-height: 3.5rem;
    display: flex;
    align-items: center;
}

.select2-container--default .select2-selection--single:focus,
.select2-container--default.select2-container--focus .select2-selection--single {
    outline: none !important;
    border-color: var(--primary-500) !important;
    box-shadow: 0 0 0 4px rgba(168, 85, 247, 0.1) !important;
    transform: translateY(-2px);
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: var(--text-primary) !important;
    line-height: 1.5;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    height: 100%;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: var(--text-tertiary) !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 100% !important;
    right: var(--space-4) !important;
    top: 0 !important;
    width: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: var(--text-secondary) transparent transparent transparent !important;
    border-style: solid;
    border-width: 6px 6px 0 6px;
    height: 0;
    left: 0;
    margin: 0;
    position: static;
    top: 0;
    width: 0;
    transition: all var(--transition-fast);
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent var(--primary-500) transparent !important;
    border-width: 0 6px 6px 6px;
}

/* Select2 Clear Button */
.select2-container--default .select2-selection--single .select2-selection__clear {
    color: var(--text-secondary) !important;
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-right: var(--space-6);
    padding: var(--space-1);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    background: var(--gray-100);
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
}

.select2-container--default .select2-selection--single .select2-selection__clear:hover {
    color: var(--error) !important;
    background: var(--gray-200);
    transform: scale(1.1);
}

/* Select2 Dropdown */
.select2-dropdown {
    border: 1px solid var(--border-light) !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: var(--shadow-xl) !important;
    background: var(--bg-primary) !important;
    margin-top: var(--space-2);
    overflow: hidden;
}

.select2-dropdown--below {
    border-top: none !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
    margin-top: 0;
}

.select2-dropdown--above {
    border-bottom: none !important;
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    margin-bottom: 0;
}

/* Select2 Search */
.select2-search--dropdown {
    padding: var(--space-4) !important;
    background: var(--gray-50) !important;
    border-bottom: 1px solid var(--border-light) !important;
}

.select2-search--dropdown .select2-search__field {
    padding: var(--space-3) var(--space-4) !important;
    border: 1px solid var(--border-medium) !important;
    border-radius: var(--radius-lg) !important;
    font-size: var(--text-sm) !important;
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    width: 100% !important;
    box-sizing: border-box !important;
    transition: all var(--transition-fast);
}

.select2-search--dropdown .select2-search__field:focus {
    outline: none !important;
    border-color: var(--primary-500) !important;
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1) !important;
}

/* Select2 Results */
.select2-results {
    background: var(--bg-primary) !important;
    max-height: 300px !important;
}

.select2-results__options {
    max-height: 250px !important;
    overflow-y: auto !important;
    padding: var(--space-2) 0 !important;
}

.select2-results__option {
    padding: var(--space-3) var(--space-4) !important;
    color: var(--text-secondary) !important;
    cursor: pointer !important;
    transition: all var(--transition-fast) !important;
    border-radius: 0 !important;
    font-size: var(--text-sm) !important;
    line-height: 1.5;
    margin: 0 var(--space-2);
    border-radius: var(--radius-md);
}

.select2-results__option:hover,
.select2-results__option--highlighted {
    background: var(--primary-50) !important;
    color: var(--primary-600) !important;
    transform: translateX(2px);
}

.select2-results__option[aria-selected="true"] {
    background: var(--primary-100) !important;
    color: var(--primary-700) !important;
    font-weight: var(--font-medium);
}

.select2-results__option[aria-selected="true"]:hover {
    background: var(--primary-200) !important;
}

/* Select2 No Results */
.select2-results__option--nodata {
    color: var(--text-tertiary) !important;
    text-align: center !important;
    font-style: italic;
    padding: var(--space-6) var(--space-4) !important;
}

/* Select2 Loading */
.select2-results__option--loading {
    color: var(--text-tertiary) !important;
    text-align: center !important;
    padding: var(--space-6) var(--space-4) !important;
}

/* Custom Scrollbar for Select2 */
.select2-results__options::-webkit-scrollbar {
    width: 6px;
}

.select2-results__options::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

.select2-results__options::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

.select2-results__options::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Condition Select Specific Styling */
.condition-select.select2-hidden-accessible + .select2-container .select2-selection--single {
    padding: var(--space-3) var(--space-4);
    min-height: 2.75rem;
    font-size: var(--text-sm);
    border: 1px solid var(--border-medium) !important;
    border-radius: var(--radius-lg) !important;
}

.condition-select.select2-hidden-accessible + .select2-container .select2-selection--single:focus,
.condition-select.select2-hidden-accessible + .select2-container.select2-container--focus .select2-selection--single {
    border-color: var(--primary-500) !important;
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1) !important;
    transform: none;
}

/* Column Select Specific Styling */
.column-select.select2-hidden-accessible + .select2-container .select2-selection--single {
    padding: var(--space-3) var(--space-4);
    min-height: 2.75rem;
    font-size: var(--text-sm);
    border: 1px solid var(--border-medium) !important;
    border-radius: var(--radius-lg) !important;
}

.column-select.select2-hidden-accessible + .select2-container .select2-selection--single:focus,
.column-select.select2-hidden-accessible + .select2-container.select2-container--focus .select2-selection--single {
    border-color: var(--primary-500) !important;
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1) !important;
    transform: none;
}

/* Responsive Select2 */
@media (max-width: 768px) {
    .select2-container--default .select2-selection--single {
        padding: var(--space-3) var(--space-4);
        min-height: 3rem;
        font-size: var(--text-sm);
    }

    .select2-dropdown {
        border-radius: var(--radius-lg) !important;
    }

    .select2-results__options {
        max-height: 200px !important;
    }
}

/* ==========================================================================
   ENHANCED TABLE ELEMENTS
   ========================================================================== */

/* Table Header Enhancements */
.sortable-header {
    position: relative;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-bottom: 3px solid #cbd5e1;
    transition: all var(--transition-fast);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.sortable-header.sorted-asc,
.sortable-header.sorted-desc {
    background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
    border-bottom-color: #6366f1;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-5);
    gap: var(--space-3);
    position: relative;
    max-height: 32px;
}

.header-text {
    font-weight: var(--font-bold);
    color: #334155;
    font-size: var(--text-sm);
    position: relative;
    cursor: pointer;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    flex: 1;
    letter-spacing: 0.025em;
    text-transform: uppercase;
}

.header-text:hover {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

/* Sort indicators */
.sortable-header.sorted-asc .header-text::after {
    content: "▲";
    margin-left: var(--space-2);
    font-size: var(--text-xs);
    color: #6366f1;
    font-weight: var(--font-bold);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.sortable-header.sorted-desc .header-text::after {
    content: "▼";
    margin-left: var(--space-2);
    font-size: var(--text-xs);
    color: #6366f1;
    font-weight: var(--font-bold);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Enhanced Filter Icon */
.filter-icon {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 2px solid #e2e8f0;
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.25rem;
    height: 2.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.filter-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(168, 85, 247, 0.1), transparent);
    transition: left 0.3s;
}

.filter-icon:hover::before {
    left: 100%;
}

.filter-icon:hover {
    background: linear-gradient(135deg, #faf5ff, #f3e8ff);
    border-color: #a855f7;
    transform: scale(1.1) translateY(-1px);
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.2);
}

.filter-icon.filtered {
    background: linear-gradient(135deg, #a855f7, #9333ea);
    border-color: #9333ea;
    color: var(--text-inverse);
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.4);
}

.filter-icon.filtered:hover {
    background: linear-gradient(135deg, #9333ea, #7c3aed);
    box-shadow: 0 6px 16px rgba(168, 85, 247, 0.5);
}

.filter-svg {
    width: 1.125rem;
    height: 1.125rem;
    stroke: currentColor;
    transition: all var(--transition-fast);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.filter-icon:hover .filter-svg {
    stroke-width: 2.5;
    transform: scale(1.1);
}

/* Actions Cell Styling */
.actions-cell {
    width: 140px;
    min-width: 140px;
    padding: var(--space-3) var(--space-4);
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border-right: 3px solid #e2e8f0;
    position: sticky;
    left: 0;
    z-index: 10;
    box-shadow: 4px 0 8px rgba(0, 0, 0, 0.05);
}

.actions-container {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    justify-content: center;
}

/* Enhanced Info Button */
.info-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--space-2);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.75rem;
    height: 2.75rem;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.info-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.info-btn:hover::before {
    left: 100%;
}

.info-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.info-btn:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.info-icon {
    width: 1.25rem;
    height: 1.25rem;
    stroke: currentColor;
    stroke-width: 2.5;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Enhanced Checkbox Styling */
.checkbox-container {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkbox-checkmark {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.25rem;
    height: 2.25rem;
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 2px solid #cbd5e1;
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.checkbox-checkmark::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(168, 85, 247, 0.1), transparent);
    transition: left 0.3s;
}

.checkbox-container:hover .checkbox-checkmark::before {
    left: 100%;
}

.checkbox-container:hover .checkbox-checkmark {
    border-color: #a855f7;
    background: linear-gradient(135deg, #faf5ff, #f3e8ff);
    transform: scale(1.08);
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.2);
}

.checkbox-input:checked + .checkbox-checkmark {
    background: linear-gradient(135deg, #a855f7, #9333ea);
    border-color: #9333ea;
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.4);
}

.checkbox-input:checked + .checkbox-checkmark:hover {
    background: linear-gradient(135deg, #9333ea, #7c3aed);
    box-shadow: 0 6px 16px rgba(168, 85, 247, 0.5);
}

.checkbox-icon {
    width: 1.125rem;
    height: 1.125rem;
    stroke: var(--text-inverse);
    stroke-width: 3.5;
    opacity: 0;
    transform: scale(0.6) rotate(-10deg);
    transition: all var(--transition-fast);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.checkbox-input:checked + .checkbox-checkmark .checkbox-icon {
    opacity: 1;
    transform: scale(1) rotate(0deg);
}

/* Focus states for accessibility */
.checkbox-input:focus + .checkbox-checkmark {
    outline: 3px solid rgba(168, 85, 247, 0.3);
    outline-offset: 2px;
}

.info-btn:focus {
    outline: 3px solid rgba(59, 130, 246, 0.3);
    outline-offset: 2px;
}

.filter-icon:focus {
    outline: 3px solid rgba(168, 85, 247, 0.3);
    outline-offset: 2px;
}
