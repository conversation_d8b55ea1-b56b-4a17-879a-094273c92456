{"GlobalPropertiesHash": "x5ywim1cBX+NFcVY6lw5iX+JUcT37VqxQSQLDm757Wo=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["RGOqu0qLxFayFIxPbAN1H0jRZrgjznQs/Jr+5D+SqCk=", "6xAvvRoqBw7h/7aIrShwogc0Krun3Vqn+Vk708m13GA=", "sgiSX07ig76MsE4VenTtDJ6B75oANkkSRyJ8fSHLlNo=", "sDr3Hk5mGVi7Xy085Iy1kRdSB9QU8wJtgEtJxIVYgPM=", "zbO/PsgfLNvCcZXdc7JC8bWlV2S5jQRILcPs3b6uMM4=", "wkULmegifHUcNp9u5NdoxwX2ObTJJ140QJg+VekKYyY=", "fI//4jEJ8/pGER90HCe+2UZf1DJdP2TvFXGeIgOkBgQ=", "BPlImXmWflQK9KPUGowvVf5dFhjLQhHBQ6O/zHtyxSY=", "zIzt9/SuF145aBzgNVcvUlUZNTd25CYJFp+1bZbZLe0=", "4So/FRc6+TOn6WxWkwy20AskYDrIwBh0EcsT+w32Uow=", "XBlw+8HDp/8kRcKNnmAPw0XfZjisHqBal+q5DubhcPM=", "HNEMW4BWxtebRoCEtGbsMvbDgTBXKxeUx5GdLlGh5Q0=", "ie7Qef4w+pRYHRwTPDKjBgDQXW/v+AMx4LfRANNDJxM=", "ZhVR0RggcC2c/QItICZvuQIMBs2IcqpqQGpu5g3cG5o=", "rW6BUIGAOOqMgdBNV04c8adq307Vn/1bHzVYpAIcvXQ=", "isEFwvh4hq6CCBYlUxszO7Hm+jQZtF7L5Zje/1bCQuo=", "XFFuZ84Gueh649wDxlea53lKd4Vz1vjGwDYHNcBakxE=", "iwHjagw7tUF7g8L/2L8z3b2/ya4KsW9eFeZDHsYtRIs=", "WRfrEHos5hHnm8UfiZOFessEd8LIN8+NFozd6QYrgRY=", "H9zuASb9oHrv10U6By5MAcuf9v0MjAZWoK4AaMJoEYo=", "gT5uf6WSrF+OeDXsanGLoP/E9jp7ZBTybSmyP0mfZoo=", "XZwIg+c+IOozR2BRedVn4FPkm/2pMKBKDsvjDy38WbE=", "tCV3fW04WbedwuL6gbWQ7n4rI2k6XCNPTJIMoruF+H4=", "E5MVfX+j7FgiiMUn0ekYIaKS3ZoqHc8OH6ZKU/GVWGc=", "7nxeWpGK/zqyyUH8GmOwMGZVf4M13K5gxbKvl5J2E50=", "MWbHMn5OWXiC4QTTsXNfB0kQiGgoSLvdoQxw3go61sk=", "lxw2qPxr97ux8LOL8VpGNYLROgFMEUFKHMOssxx6ue4=", "AhEkaDP8Ko8564Iav0FBjmBboE8LlX71EUuVheSZgdA=", "f2tu3Nj5G+A08DgmWiA872SWIPtEEgENZ+lKXiKy2HA=", "pBoSuBFe6RV4KB6iZha2JDPcgJK1zBU+vKoEi3X10S8=", "EYfMBMFp0nGru70x/IolINmFacSF4EbNzpi6gMWH0N0=", "XPQNjyb5T0DyA8zJOCqH6YnFYX24xHl1kVyjnqTtp94=", "PwUrzBl2iI/IhuwqAqkjyTFZ0DDkUWqRSW2UbOhXYT0=", "A4RvwDNX4cghELWnGBCJmPNPJyaLuLIoCw4WUoRDwME=", "eO5Z41R+7TljBL3rYZKsQ7BZghJ8rgNeKxrYaApV9j8=", "Q8A7CjfDD3OyCExs/i+fISYGX+3Yr7uFju50Pj6OE3E=", "qSlgS4ljV4ylILexLlnpUzTPCVRV0StfUqV2hMWvfmo=", "O/PJ3+wYgG4dO8K9ksTRZo03K8WRlwhDgNUxHxreTRU=", "9+7zZVfI40zbV9XvWVyDVOPcSicRLhMLi6T+2igqRGU=", "NIQGi+exLOtZFt3ShU+DWsjR66pVx3vTeuyagxLdSX8=", "Emyc7KXKCYQhRrizRAoemAKzeCBXVKJA3XXo5cqFpWw=", "mJTP5cPGyqpYUz9TrzEibc8/iYsM2GZF3YLLAeZD2O4=", "qQPOHGcjc917bPlL9FRsY3HmQRKku3JAkqGfO2Gb6LA=", "FOU/zIJyg8brfEEd2QP5y2qghL0U2vYVuQ+A08WOIRE=", "6/sx7qVvqqEFWYX951IcTNIIHGqR4sf2hF46vd9W9DQ=", "gY2VVqYqLgQkmRfELUiM83She8LAnR/kttjcnCGdDco=", "5YeXI++EjM3b+7wLSAXD0b61yjVNJ2en1gny2PyOnxo=", "gQCXxmlAwh77X1ZABQgigk+V91Wg0pUjLjncYwveF38=", "/+7iqmf0TI4p3iMTJSApo5cWY5xpDg1LjvfmPbb2Aow=", "jRgIPIQSsBF8ieUjENzui+n/Zgu86QLk5DCFJTQnqCU=", "m29PVrLjDP+Zso4ML0xGWlLPMnZ2Fit1Ae+IHqLlJX0=", "YN72C2FUKRyNi/1uA7AYT7+UYjqobbBLeLChgUlZwBU=", "9PkLXzsZqTr5/kRsuY3sMGk9Lj3lzKJ4SrkhXaZgOOU=", "heqDmj7aqTjxNa+Aln8f3HhHD/EemU0locMcD4mY0TQ=", "YBoySEO2VfKaMbzEfIzrOktG+IsM0B3nhaPOlOtlOGU=", "IzEw7JFjEsoHpePAeOYVHLeL2tRMMCPgBk081UyvtA4=", "XGX9vRq5IHpQ0jvx/35NsRldNghg8MPib0PQFw57Kok=", "Uuy30lo51v/KVEKqZp7HWmc+FFnL+/ZQrhu4nI2FJbQ=", "hlM4NB5/al+hegU3RIjgw1L0Yx/UU9b6yWEcl+oQwnw=", "i64HYX1nVq4WVsu4yEXyHfusZd9SQY7uF71VwLlfOBs=", "TAMqf1uAMEdcq/K5UZWQ2LvOefoRiawnSfsicbebwT8=", "Tb9BGjTFVVYEbPdbuNeI/g+3jXytohaVZHft23087Zw=", "x2dhvQmif2QUWCryJX8NpfocHS6+FZPSGEGVrBD7LYQ=", "JTDss9vt5cbldd8HukZPoh4iRtv6aItj8Ud1mxICoVg=", "bbt0pBbW0zAUOIomwynub1HgOfS8bI/CEEpYwfmKDN0=", "oIigaUnKm8jZR8YWerLHMgjeuokfvGzUPJMYUASZ+jM=", "b6AfIMMhfGnpSv0UgQ90psEG1W6jzixlRYUwMZgIGrw=", "z2XaHqCKBLN9Eno6ZbnS2LxeL/N3s7T3clGJixBO2IY=", "t9OxUgSRPjvKSnMUxSo/DY5OjHDmDGjtEs2Uh+CG1/c=", "eidppyf8RbIqxr81Kfs1ZFMLC5srBjChmnE3m+PI0qo=", "DXzEcOifH+U4HjyihzsxAQ5DL5zk7Lrdceuiuq6WaF8=", "V49yMY+KlwOe5OOTZtxtjjIZlUyYY8evh2tL7GzO8FE=", "0s0F69L5uMrr+YEdMSuHIR368Owt23N09AXonzOYq/I=", "kA9r7wNNQESeqtlVw59maxP0jOgEdzh3cRQPXpJh2ME=", "ul+ujoPqZVfCIh6NGX3uUtye8UkVInrATNiBLzG5maY=", "Mx6ubds5UOLaZixxlm0oJJQjfEN8kS21Empuj/YoYHM=", "QKqkGoeJEaYRuA40S+gTv1a8s4Ak60+hFbQeYK1bcUY=", "Gzi07K+Sm4LPc9HJcmsFc7uMMRJrTxKFZKg6cqJCZhk=", "9hXBIPfd9u6zEzD57SRK7F6qb7wtAbhUoByQY0m8BBE="], "CachedAssets": {"RGOqu0qLxFayFIxPbAN1H0jRZrgjznQs/Jr+5D+SqCk=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\base.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/base#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pif4ri5ynq", "Integrity": "kXrVc/7UVRp9jWvd9C2EzQ5g4hZWll76jxaXh/QZv48=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\base.css", "FileLength": 8423, "LastWriteTime": "2025-05-28T06:59:40.3003223+00:00"}, "6xAvvRoqBw7h/7aIrShwogc0Krun3Vqn+Vk708m13GA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\components.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/components#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ptgn66vtkq", "Integrity": "7YKGmhELAYsDGwbDM8+UTcYHJoCmq/W4onzgJDom4Sw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\components.css", "FileLength": 22141, "LastWriteTime": "2025-05-28T09:15:10.4471871+00:00"}, "sgiSX07ig76MsE4VenTtDJ6B75oANkkSRyJ8fSHLlNo=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\error.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/error#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cnwu351nc5", "Integrity": "yOfuHwhICRdnOyGqmoI57yZ9VCBNQqFRTU45dUg7wnQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\error.css", "FileLength": 8995, "LastWriteTime": "2025-05-28T07:04:34.5328873+00:00"}, "sDr3Hk5mGVi7Xy085Iy1kRdSB9QU8wJtgEtJxIVYgPM=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\layout.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/layout#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "foco3ldz1j", "Integrity": "0cKevDKmrcPzhEKPdiec6ncMkOV4FQTWQ1XX0G3rnUY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\layout.css", "FileLength": 7356, "LastWriteTime": "2025-05-28T10:38:36.357744+00:00"}, "zbO/PsgfLNvCcZXdc7JC8bWlV2S5jQRILcPs3b6uMM4=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\transaction-inquiry.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/transaction-inquiry#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7f4923<PERSON><PERSON><PERSON>", "Integrity": "o3z1SxCKyE0ltdljDbf5cZe4DQAWoiwsakSnj/RD+f4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\transaction-inquiry.css", "FileLength": 40141, "LastWriteTime": "2025-05-28T09:14:17.922513+00:00"}, "wkULmegifHUcNp9u5NdoxwX2ObTJJ140QJg+VekKYyY=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\css\\view-builder.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "css/view-builder#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3tjp6gaf54", "Integrity": "eWlozSXE1J631rCRgficVRvqaHRns2d8rSvfbY8qXwM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\view-builder.css", "FileLength": 12898, "LastWriteTime": "2025-05-28T09:15:36.246787+00:00"}, "fI//4jEJ8/pGER90HCe+2UZf1DJdP2TvFXGeIgOkBgQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\favicon.ico", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-05-19T11:29:54.0903807+00:00"}, "BPlImXmWflQK9KPUGowvVf5dFhjLQhHBQ6O/zHtyxSY=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\js\\site.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3r8b2a0bt1", "Integrity": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 3, "LastWriteTime": "2025-06-11T09:49:08.2536192+00:00"}, "zIzt9/SuF145aBzgNVcvUlUZNTd25CYJFp+1bZbZLe0=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\js\\transactionInquiry.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "js/transactionInquiry#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7ptw6gpwez", "Integrity": "W/RV/LGFNQ8QAiDgwd4vfBeN/WYPaekLj1wuxbFR2wI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\transactionInquiry.js", "FileLength": 57931, "LastWriteTime": "2025-06-10T12:09:39.2505375+00:00"}, "4So/FRc6+TOn6WxWkwy20AskYDrIwBh0EcsT+w32Uow=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\js\\viewBuilder.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "js/viewBuilder#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bhvz7uspg0", "Integrity": "4daJ343Lohqc2o2eEgV1IU1VD8o98AevSGkyZCbEajM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\viewBuilder.js", "FileLength": 17850, "LastWriteTime": "2025-06-10T11:20:57.008249+00:00"}, "XBlw+8HDp/8kRcKNnmAPw0XfZjisHqBal+q5DubhcPM=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-05-19T11:29:53.9482875+00:00"}, "HNEMW4BWxtebRoCEtGbsMvbDgTBXKxeUx5GdLlGh5Q0=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-05-19T11:29:53.9492749+00:00"}, "ie7Qef4w+pRYHRwTPDKjBgDQXW/v+AMx4LfRANNDJxM=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-05-19T11:29:53.9502756+00:00"}, "ZhVR0RggcC2c/QItICZvuQIMBs2IcqpqQGpu5g3cG5o=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-05-19T11:29:53.9506445+00:00"}, "rW6BUIGAOOqMgdBNV04c8adq307Vn/1bHzVYpAIcvXQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-05-19T11:29:53.9517842+00:00"}, "isEFwvh4hq6CCBYlUxszO7Hm+jQZtF7L5Zje/1bCQuo=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-05-19T11:29:53.9587866+00:00"}, "XFFuZ84Gueh649wDxlea53lKd4Vz1vjGwDYHNcBakxE=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-05-19T11:29:53.9587866+00:00"}, "iwHjagw7tUF7g8L/2L8z3b2/ya4KsW9eFeZDHsYtRIs=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-05-19T11:29:53.959786+00:00"}, "WRfrEHos5hHnm8UfiZOFessEd8LIN8+NFozd6QYrgRY=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-05-19T11:29:53.959786+00:00"}, "H9zuASb9oHrv10U6By5MAcuf9v0MjAZWoK4AaMJoEYo=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-05-19T11:29:53.9607858+00:00"}, "gT5uf6WSrF+OeDXsanGLoP/E9jp7ZBTybSmyP0mfZoo=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-05-19T11:29:53.9607858+00:00"}, "XZwIg+c+IOozR2BRedVn4FPkm/2pMKBKDsvjDy38WbE=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-05-19T11:29:53.9607858+00:00"}, "tCV3fW04WbedwuL6gbWQ7n4rI2k6XCNPTJIMoruF+H4=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-05-19T11:29:53.9617859+00:00"}, "E5MVfX+j7FgiiMUn0ekYIaKS3ZoqHc8OH6ZKU/GVWGc=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-05-19T11:29:53.9617859+00:00"}, "7nxeWpGK/zqyyUH8GmOwMGZVf4M13K5gxbKvl5J2E50=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-05-19T11:29:53.9617859+00:00"}, "MWbHMn5OWXiC4QTTsXNfB0kQiGgoSLvdoQxw3go61sk=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-05-19T11:29:53.9627867+00:00"}, "lxw2qPxr97ux8LOL8VpGNYLROgFMEUFKHMOssxx6ue4=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-05-19T11:29:53.9627867+00:00"}, "AhEkaDP8Ko8564Iav0FBjmBboE8LlX71EUuVheSZgdA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-05-19T11:29:53.9637856+00:00"}, "f2tu3Nj5G+A08DgmWiA872SWIPtEEgENZ+lKXiKy2HA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-05-19T11:29:53.9647859+00:00"}, "pBoSuBFe6RV4KB6iZha2JDPcgJK1zBU+vKoEi3X10S8=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-05-19T11:29:53.9647859+00:00"}, "EYfMBMFp0nGru70x/IolINmFacSF4EbNzpi6gMWH0N0=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-05-19T11:29:53.9647859+00:00"}, "XPQNjyb5T0DyA8zJOCqH6YnFYX24xHl1kVyjnqTtp94=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-05-19T11:29:53.9657859+00:00"}, "PwUrzBl2iI/IhuwqAqkjyTFZ0DDkUWqRSW2UbOhXYT0=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-05-19T11:29:53.9667855+00:00"}, "A4RvwDNX4cghELWnGBCJmPNPJyaLuLIoCw4WUoRDwME=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-05-19T11:29:53.9673025+00:00"}, "eO5Z41R+7TljBL3rYZKsQ7BZghJ8rgNeKxrYaApV9j8=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-05-19T11:29:53.9684301+00:00"}, "Q8A7CjfDD3OyCExs/i+fISYGX+3Yr7uFju50Pj6OE3E=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-05-19T11:29:53.9704323+00:00"}, "qSlgS4ljV4ylILexLlnpUzTPCVRV0StfUqV2hMWvfmo=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-05-19T11:29:53.9714289+00:00"}, "O/PJ3+wYgG4dO8K9ksTRZo03K8WRlwhDgNUxHxreTRU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-05-19T11:29:53.9724288+00:00"}, "9+7zZVfI40zbV9XvWVyDVOPcSicRLhMLi6T+2igqRGU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-05-19T11:29:53.9734286+00:00"}, "NIQGi+exLOtZFt3ShU+DWsjR66pVx3vTeuyagxLdSX8=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-05-19T11:29:53.9754276+00:00"}, "Emyc7KXKCYQhRrizRAoemAKzeCBXVKJA3XXo5cqFpWw=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-05-19T11:29:53.9764271+00:00"}, "mJTP5cPGyqpYUz9TrzEibc8/iYsM2GZF3YLLAeZD2O4=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-05-19T11:29:53.9774272+00:00"}, "qQPOHGcjc917bPlL9FRsY3HmQRKku3JAkqGfO2Gb6LA=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-05-19T11:29:53.9784276+00:00"}, "FOU/zIJyg8brfEEd2QP5y2qghL0U2vYVuQ+A08WOIRE=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-05-19T11:29:53.9804272+00:00"}, "6/sx7qVvqqEFWYX951IcTNIIHGqR4sf2hF46vd9W9DQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-05-19T11:29:53.9814273+00:00"}, "gY2VVqYqLgQkmRfELUiM83She8LAnR/kttjcnCGdDco=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-05-19T11:29:53.982427+00:00"}, "5YeXI++EjM3b+7wLSAXD0b61yjVNJ2en1gny2PyOnxo=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-05-19T11:29:53.9834272+00:00"}, "gQCXxmlAwh77X1ZABQgigk+V91Wg0pUjLjncYwveF38=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-05-19T11:29:53.9852051+00:00"}, "/+7iqmf0TI4p3iMTJSApo5cWY5xpDg1LjvfmPbb2Aow=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-05-19T11:29:53.9852051+00:00"}, "jRgIPIQSsBF8ieUjENzui+n/Zgu86QLk5DCFJTQnqCU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-05-19T11:29:53.9898904+00:00"}, "m29PVrLjDP+Zso4ML0xGWlLPMnZ2Fit1Ae+IHqLlJX0=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-05-19T11:29:53.9898904+00:00"}, "YN72C2FUKRyNi/1uA7AYT7+UYjqobbBLeLChgUlZwBU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-05-19T11:29:53.9918899+00:00"}, "9PkLXzsZqTr5/kRsuY3sMGk9Lj3lzKJ4SrkhXaZgOOU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-05-19T11:29:53.9958765+00:00"}, "heqDmj7aqTjxNa+Aln8f3HhHD/EemU0locMcD4mY0TQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-05-19T11:29:53.9968759+00:00"}, "YBoySEO2VfKaMbzEfIzrOktG+IsM0B3nhaPOlOtlOGU=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-05-19T11:29:53.9948699+00:00"}, "IzEw7JFjEsoHpePAeOYVHLeL2tRMMCPgBk081UyvtA4=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-05-19T11:29:54.0913804+00:00"}, "XGX9vRq5IHpQ0jvx/35NsRldNghg8MPib0PQFw57Kok=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-05-19T11:29:54.0913804+00:00"}, "Uuy30lo51v/KVEKqZp7HWmc+FFnL+/ZQrhu4nI2FJbQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-05-19T11:29:54.0923801+00:00"}, "hlM4NB5/al+hegU3RIjgw1L0Yx/UU9b6yWEcl+oQwnw=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-05-19T11:29:53.9462733+00:00"}, "i64HYX1nVq4WVsu4yEXyHfusZd9SQY7uF71VwLlfOBs=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-05-19T11:29:53.9462733+00:00"}, "TAMqf1uAMEdcq/K5UZWQ2LvOefoRiawnSfsicbebwT8=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-05-19T11:29:53.9472738+00:00"}, "Tb9BGjTFVVYEbPdbuNeI/g+3jXytohaVZHft23087Zw=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-05-19T11:29:53.9472738+00:00"}, "x2dhvQmif2QUWCryJX8NpfocHS6+FZPSGEGVrBD7LYQ=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-05-19T11:29:54.0903807+00:00"}, "JTDss9vt5cbldd8HukZPoh4iRtv6aItj8Ud1mxICoVg=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-05-19T11:29:53.9432732+00:00"}, "bbt0pBbW0zAUOIomwynub1HgOfS8bI/CEEpYwfmKDN0=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-05-19T11:29:53.9442731+00:00"}, "oIigaUnKm8jZR8YWerLHMgjeuokfvGzUPJMYUASZ+jM=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-05-19T11:29:53.9452731+00:00"}, "b6AfIMMhfGnpSv0UgQ90psEG1W6jzixlRYUwMZgIGrw=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-05-19T11:29:53.9958765+00:00"}, "z2XaHqCKBLN9Eno6ZbnS2LxeL/N3s7T3clGJixBO2IY=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\select2\\css\\select2.min.css", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/select2/css/select2.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "66wye7e524", "Integrity": "XkFWKlPlxi9E2fiymqJAfajRHhFVdCdCr3BJfZ0jkNs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\css\\select2.min.css", "FileLength": 9632, "LastWriteTime": "2025-05-28T09:17:55.7695335+00:00"}, "t9OxUgSRPjvKSnMUxSo/DY5OjHDmDGjtEs2Uh+CG1/c=": {"Identity": "D:\\VS Projects\\Historical_Web\\wwwroot\\lib\\select2\\js\\select2.min.js", "SourceId": "Historical_Web", "SourceType": "Discovered", "ContentRoot": "D:\\VS Projects\\Historical_Web\\wwwroot\\", "BasePath": "_content/Historical_Web", "RelativePath": "lib/select2/js/select2.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z1f3u68ax1", "Integrity": "9yRP/2EFlblE92vzCA10469Ctd0jT48HnmmMw5rJZrA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\js\\select2.min.js", "FileLength": 73163, "LastWriteTime": "2025-05-28T09:25:01.2662016+00:00"}}, "CachedCopyCandidates": {}}