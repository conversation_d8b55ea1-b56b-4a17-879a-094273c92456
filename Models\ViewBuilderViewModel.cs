using System.ComponentModel.DataAnnotations;

namespace Historical_Web.Models
{
    public class ViewBuilderViewModel
    {
        public int? ViewID { get; set; }

        [Required]
        [Display(Name = "View Name")]
        public string ViewName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Table Name")]
        public string TableName { get; set; } = string.Empty;

        public List<string> AvailableTables { get; set; } = new List<string>();
        public List<GridView> ExistingViews { get; set; } = new List<GridView>();
        public List<ColumnMapping> ColumnMappings { get; set; } = new List<ColumnMapping>();

        public bool IsEditMode => ViewID.HasValue && ViewID.Value > 0;
    }

    public class ColumnMapping
    {
        public string ColumnName { get; set; } = string.Empty;
        public string Alias { get; set; } = string.Empty;
        public int Order { get; set; }
    }
}
