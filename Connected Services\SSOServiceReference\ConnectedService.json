{"ExtendedData": {"inputs": ["https://plz-secusit-v01.nbe.ahly.bank/SSOService/Service.asmx"], "collectionTypes": ["System.Array", "System.Collections.Generic.Dictionary`2"], "namespaceMappings": ["*, SSOServiceReference"], "references": ["Azure.Core, {Azure.Core, 1.38.0}", "Azure.Identity, {Azure.Identity, 1.11.4}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 1.1.1}", "Microsoft.Data.SqlClient, {Microsoft.Data.SqlClient, 5.1.6}", "Microsoft.EntityFrameworkCore, {Microsoft.EntityFrameworkCore, 9.0.5}", "Microsoft.EntityFrameworkCore.Abstractions, {Microsoft.EntityFrameworkCore.Abstractions, 9.0.5}", "Microsoft.EntityFrameworkCore.Relational, {Microsoft.EntityFrameworkCore.Relational, 9.0.5}", "Microsoft.EntityFrameworkCore.SqlServer, {Microsoft.EntityFrameworkCore.SqlServer, 9.0.5}", "Microsoft.Extensions.Caching.Abstractions, {Microsoft.Extensions.Caching.Abstractions, 9.0.5}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 9.0.5}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 9.0.5}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 9.0.5}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 9.0.5}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 9.0.5}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 9.0.5}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 9.0.5}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 9.0.5}", "Microsoft.Identity.Client, {Microsoft.Identity.Client, 4.61.3}", "Microsoft.Identity.Client.Extensions.Msal, {Microsoft.Identity.Client.Extensions.Msal, 4.61.3}", "Microsoft.IdentityModel.Abstractions, {Microsoft.IdentityModel.Abstractions, 6.35.0}", "Microsoft.IdentityModel.JsonWebTokens, {Microsoft.IdentityModel.JsonWebTokens, 6.35.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.35.0}", "Microsoft.IdentityModel.Protocols, {Microsoft.IdentityModel.Protocols, 6.35.0}", "Microsoft.IdentityModel.Protocols.OpenIdConnect, {Microsoft.IdentityModel.Protocols.OpenIdConnect, 6.35.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.35.0}", "Microsoft.SqlServer.Server, {Microsoft.SqlServer.Server, 1.0.0}", "Newtonsoft.J<PERSON>, {Newtonsoft<PERSON>J<PERSON>, 13.0.3}", "System.ClientModel, {System.ClientModel, 1.0.0}", "System.Diagnostics.DiagnosticSource, {System.Diagnostics.DiagnosticSource, 9.0.5}", "System.IdentityModel.Tokens.Jwt, {System.IdentityModel.Tokens.Jwt, 6.35.0}", "System.IO.Pipelines, {System.IO.Pipelines, 9.0.5}", "System.Memory.Data, {System.Memory.Data, 1.0.2}", "System.Runtime, {System.Runtime, 4.3.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 5.0.0}", "System.Security.Cryptography.ProtectedData, {System.Security.Cryptography.ProtectedData, 6.0.0}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 9.0.5}", "System.<PERSON>.<PERSON>, {System.Text.<PERSON>, 9.0.5}"], "targetFramework": "net8.0", "typeReuseMode": "All"}}