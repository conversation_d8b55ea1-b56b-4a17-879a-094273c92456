<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.ServiceModel.Http" Version="8.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="8.*" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="8.*" />
  </ItemGroup>
</Project>