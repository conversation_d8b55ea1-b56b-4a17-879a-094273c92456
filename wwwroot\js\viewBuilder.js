// View Builder JavaScript
$(document).ready(function() {
    initializeViewBuilder();
});

let availableColumns = [];
let currentViewId = null;

function initializeViewBuilder() {
    // Initialize Select2 on main dropdown with allowClear
    $('#tableName').select2({
        placeholder: 'Select a table...',
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: false,
        minimumResultsForSearch: 5
    });

    // Event handlers
    $('#tableName').on('change', handleTableChange);
    $('#addColumnBtn').on('click', addColumnMapping);
    $('#viewBuilderForm').on('submit', handleFormSubmit);
    $('#saveViewBtn').on('click', handleSaveButtonClick);
    $('#resetFormBtn').on('click', resetForm);
    $('#deleteViewBtn').on('click', handleDeleteView);

    // Removed header button handlers - elements no longer exist

    // Existing view item clicks
    $(document).on('click', '[data-view-id]', function() {
        const viewId = $(this).data('view-id');
        loadView(viewId);
    });

    $(document).on('click', 'button[data-view-id]', function(e) {
        e.stopPropagation();
        const viewId = $(this).data('view-id');
        loadView(viewId);
    });

    // Column mapping events
    $(document).on('click', '.column-remove', function() {
        removeColumnMapping.call(this);
    });
    $(document).on('change', 'select[name="columnName"]', handleColumnSelectChange);

    // Make column mappings sortable
    initializeSortable();
}

function handleTableChange() {
    const tableName = $('#tableName').val();

    if (!tableName) {
        $('#addColumnBtn').prop('disabled', true);
        availableColumns = [];
        showEmptyState();
        return;
    }

    // Show loading state
    $('#addColumnBtn').prop('disabled', true).html('Loading...');

    // Fetch table columns
    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/ViewBuilder/GetTableColumns',
        type: 'GET',
        data: { tableName: tableName },
        success: function(columns) {
            availableColumns = columns;
            $('#addColumnBtn').prop('disabled', false).html('Add Column');

            if ($('#columnMappingsContainer [data-mapping-id]').length === 0) {
                showEmptyState();
            }
        },
        error: function(xhr) {
            showAlert('Error loading table columns: ' + xhr.responseText, 'danger');
            $('#addColumnBtn').prop('disabled', true).html('Add Column');
        }
    });
}

function addColumnMapping() {
    if (availableColumns.length === 0) {
        showAlert('Please select a table before adding columns.', 'warning');
        return;
    }

    hideEmptyState();

    const mappingId = Date.now();
    const usedColumns = getUsedColumns();
    const availableOptions = availableColumns.filter(col => !usedColumns.includes(col));

    if (availableOptions.length === 0) {
        showAlert('All available columns have already been added.', 'info');
        return;
    }

    const mappingHtml = `
        <div class="column-mapping" data-mapping-id="${mappingId}">
            <div class="column-mapping-content">
                <div class="column-field">
                    <label class="column-label">Column Name</label>
                    <select name="columnName" class="column-select">
                        <option value="">Select column...</option>
                        ${availableOptions.map(col => `<option value="${col}">${col}</option>`).join('')}
                    </select>
                </div>
                <div class="column-field">
                    <label class="column-label">Alias</label>
                    <input type="text" name="alias" class="column-input" placeholder="Enter a custom alias for this column">
                </div>
                <button type="button" class="column-remove">
                    🗑️
                </button>
            </div>
        </div>
    `;

    $('#columnMappingsContainer').append(mappingHtml);

    // Initialize Select2 on the new column select
    const $newMapping = $(`[data-mapping-id="${mappingId}"]`);
    $newMapping.find('.column-select').select2({
        placeholder: 'Choose a column...',
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: false,
        minimumResultsForSearch: 5
    });

    updateFormState();
}

function removeColumnMapping() {
    $(this).closest('[data-mapping-id]').remove();

    if ($('#columnMappingsContainer [data-mapping-id]').length === 0) {
        showEmptyState();
    }

    updateFormState();
}

function handleColumnSelectChange() {
    const $select = $(this);
    const $aliasInput = $select.closest('[data-mapping-id]').find('input[name="alias"]');
    const columnName = $select.val();

    if (columnName && !$aliasInput.val()) {
        // Auto-generate alias from column name
        const alias = columnName.replace(/([A-Z])/g, ' $1').trim();
        $aliasInput.val(alias);
    }

    updateFormState();
}

function getUsedColumns() {
    const usedColumns = [];
    $('select[name="columnName"]').each(function() {
        const value = $(this).val();
        if (value) {
            usedColumns.push(value);
        }
    });
    return usedColumns;
}

function showEmptyState() {
    $('#columnMappingsContainer').html(`
        <div id="emptyState">
            <p>Please select a table above to start adding columns to your view.</p>
        </div>
    `);
}

function hideEmptyState() {
    $('#emptyState').remove();
}

function updateFormState() {
    const hasViewName = $('#viewName').val().trim() !== '';
    const hasTableName = $('#tableName').val() !== '';
    const hasColumns = $('#columnMappingsContainer [data-mapping-id]').length > 0;

    $('#saveViewBtn').prop('disabled', !(hasViewName && hasTableName && hasColumns));
}

function saveView(viewData) {
    console.log('Saving view with data:', viewData);

    const $saveBtn = $('#saveViewBtn');
    const originalText = $saveBtn.html();

    $saveBtn.prop('disabled', true).html('Saving your view, please wait...');

    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/ViewBuilder/SaveView',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(viewData),
        success: function(response) {
            console.log('Save response:', response);
            if (response.success) {
                showAlert(response.message, 'success');
                currentViewId = response.viewId;
                $('#viewId').val(currentViewId);
                $('#deleteViewBtn').show();

                // Refresh the existing views list
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('Unable to save the view. Please try again.', 'danger');
            }
        },
        error: function(xhr) {
            console.error('Save error:', xhr);
            showAlert('Unable to save the view. Please try again.', 'danger');
        },
        complete: function() {
            $saveBtn.prop('disabled', false).html(originalText);
        }
    });
}

function loadView(viewId) {
    // Configuration panel is always visible now

    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/ViewBuilder/GetView',
        type: 'GET',
        data: { id: viewId },
        success: function(response) {
            currentViewId = response.viewID;
            $('#viewId').val(currentViewId);
            $('#viewName').val(response.viewName);

            // First load the table columns, then populate mappings
            loadTableColumnsAndMappings(response.tableName, response.columnMappings);

            // Highlight the selected view in the existing views panel if it's visible
            $('[data-view-id]').each(function() {
                $(this)[0].style.backgroundColor = '';
            });
            $(`[data-view-id="${viewId}"]`)[0].style.backgroundColor = '#e6f3ff';
        },
        error: function(xhr) {
            showAlert('Unable to load the selected view. Please try again later.', 'danger');
        }
    });
}

function loadTableColumnsAndMappings(tableName, columnMappings) {
    // Set table name and trigger Select2 change
    const $tableSelect = $('#tableName');
    $tableSelect.val(tableName);
    if ($tableSelect.hasClass('select2-hidden-accessible')) {
        $tableSelect.trigger('change.select2');
    } else {
        $tableSelect.trigger('change');
    }

    // Load table columns first
    $.ajax({
        url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/ViewBuilder/GetTableColumns',
        type: 'GET',
        data: { tableName: tableName },
        success: function(columns) {
            availableColumns = columns;
            $('#addColumnBtn').prop('disabled', false).html('Add Column');

            // Now populate the column mappings
            populateColumnMappings(columnMappings);
            $('#deleteViewBtn').show();
            updateFormState();
        },
        error: function(xhr) {
            showAlert('Unable to load table columns. Please try again later.', 'danger');
            $('#addColumnBtn').prop('disabled', true).html('Add Column');
        }
    });
}

function populateColumnMappings(mappings) {
    $('#columnMappingsContainer').empty();

    if (mappings && mappings.length > 0) {
        mappings.forEach(mapping => {
            // Create the mapping row manually to ensure we have the right column available
            createColumnMappingRow(mapping);
        });
    } else {
        showEmptyState();
    }
}

function createColumnMappingRow(mapping) {
    hideEmptyState();

    const mappingId = Date.now() + Math.random(); // Ensure unique ID

    // Create options for all available columns (don't filter out used ones for existing mappings)
    const columnOptions = availableColumns.map(col =>
        `<option value="${col}" ${col === mapping.columnName ? 'selected' : ''}>${col}</option>`
    ).join('');

    const mappingHtml = `
        <div class="column-mapping" data-mapping-id="${mappingId}">
            <div class="column-mapping-content">
                <div class="column-field">
                    <label class="column-label">Column Name</label>
                    <select name="columnName" class="column-select">
                        <option value="">Select column...</option>
                        ${columnOptions}
                    </select>
                </div>
                <div class="column-field">
                    <label class="column-label">Alias</label>
                    <input type="text" name="alias" class="column-input" placeholder="Enter a custom alias for this column" value="${mapping.alias || ''}" required>
                </div>
                <button type="button" class="column-remove">
                    🗑️
                </button>
            </div>
        </div>
    `;

    $('#columnMappingsContainer').append(mappingHtml);

    // Initialize Select2 on the new column select
    const $newMapping = $(`[data-mapping-id="${mappingId}"]`);
    $newMapping.find('.column-select').select2({
        placeholder: 'Choose a column...',
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: false,
        minimumResultsForSearch: 5
    });
}

// Removed loadSelectedView function - modal no longer exists

function resetForm() {
    currentViewId = null;
    $('#viewId').val('');
    $('#viewName').val('');

    // Reset Select2 dropdown
    const $tableSelect = $('#tableName');
    $tableSelect.val(null);
    if ($tableSelect.hasClass('select2-hidden-accessible')) {
        $tableSelect.trigger('change.select2');
    } else {
        $tableSelect.trigger('change');
    }

    $('#columnMappingsContainer').empty();
    $('#deleteViewBtn').hide();
    $('[data-view-id]').each(function() {
        $(this)[0].style.backgroundColor = '';
    });
    showEmptyState();
    updateFormState();
}

// Removed panel functions - simplified layout with no panels to show/hide

function handleDeleteView() {
    if (!currentViewId) return;

    if (confirm('Are you sure you want to permanently delete this view? This action cannot be undone.')) {
        $.ajax({
            url: (window.appConfig && window.appConfig.baseUrl ? window.appConfig.baseUrl : '') + '/ViewBuilder/DeleteView',
            type: 'DELETE',
            data: { id: currentViewId },
            success: function(response) {
                if (response.success) {
                    showAlert(response.message, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('Unable to delete the view. Please try again.', 'danger');
                }
            },
            error: function(xhr) {
                showAlert('Unable to delete the view. Please try again.', 'danger');
            }
        });
    }
}

function initializeSortable() {
    // This would require a sortable library like SortableJS
    // For now, we'll implement basic drag and drop functionality
}

function showAlert(message, alertType = 'info') {
    const alertClass = `alert-${alertType}`;
    const icons = {
        success: '<i class="bi bi-check-circle-fill"></i>',
        warning: '<i class="bi bi-exclamation-triangle-fill"></i>',
        error: '<i class="bi bi-x-circle-fill"></i>',
        info: '<i class="bi bi-info-circle-fill"></i>'
    };
    
    const alertHtml = `
        <div class="alert ${alertClass}" role="alert">
            <div class="alert-icon">${icons[alertType]}</div>
            <div class="alert-content">${message}</div>
            <button type="button" class="alert-close" aria-label="Close" onclick="this.parentElement.remove()">
                <i class="bi bi-x"></i>
            </button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of the form
    $('.view-config-body').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut(300, function() {
            $(this).remove();
        });
    }, 5000);
}

// Form validation
$('#viewName, #tableName').on('input change', updateFormState);

// Additional utility functions
function validateViewName(viewName) {
    if (!viewName || viewName.trim().length < 3) {
        return 'View name must be at least 3 characters long';
    }

    if (!/^[a-zA-Z0-9\s_-]+$/.test(viewName)) {
        return 'View name can only contain letters, numbers, spaces, underscores, and hyphens';
    }

    return null;
}

function validateAlias(alias) {
    if (!alias || alias.trim().length < 1) {
        return 'Alias is required';
    }

    if (alias.length > 50) {
        return 'Alias must be 50 characters or less';
    }

    return null;
}

// Enhanced form validation
function validateForm() {
    const viewName = $('#viewName').val().trim();
    const tableName = $('#tableName').val();

    // Validate view name
    const viewNameError = validateViewName(viewName);
    if (viewNameError) {
        showAlert(viewNameError, 'danger');
        return false;
    }

    // Validate table selection
    if (!tableName) {
        showAlert('Please select a table', 'danger');
        return false;
    }

    // Validate column mappings
    const mappings = [];
    let hasErrors = false;

    $('[data-mapping-id]').each(function() {
        const columnName = $(this).find('select[name="columnName"]').val();
        const alias = $(this).find('input[name="alias"]').val().trim();

        if (!columnName) {
            showAlert('Please select a column for all mappings', 'danger');
            hasErrors = true;
            return false;
        }

        const aliasError = validateAlias(alias);
        if (aliasError) {
            showAlert(aliasError, 'danger');
            hasErrors = true;
            return false;
        }

        // Check for duplicate columns
        if (mappings.some(m => m.columnName === columnName)) {
            showAlert(`Column "${columnName}" is already mapped`, 'danger');
            hasErrors = true;
            return false;
        }

        // Check for duplicate aliases
        if (mappings.some(m => m.alias.toLowerCase() === alias.toLowerCase())) {
            showAlert(`Alias "${alias}" is already used`, 'danger');
            hasErrors = true;
            return false;
        }

        mappings.push({ columnName, alias });
    });

    if (hasErrors) {
        return false;
    }

    if (mappings.length === 0) {
        showAlert('Please add at least one column mapping', 'danger');
        return false;
    }

    return true;
}

// Handle save button click (since button is outside form)
function handleSaveButtonClick(e) {
    console.log('Save button clicked');
    e.preventDefault();
    handleFormSubmit(e);
}

// Update the form submit handler to use enhanced validation
function handleFormSubmit(e) {
    e.preventDefault();

    if (!validateForm()) {
        return;
    }

    const viewName = $('#viewName').val().trim();
    const tableName = $('#tableName').val();
    const columnMappings = [];

    $('[data-mapping-id]').each(function(index) {
        const columnName = $(this).find('select[name="columnName"]').val();
        const alias = $(this).find('input[name="alias"]').val().trim();

        columnMappings.push({
            columnName: columnName,
            alias: alias,
            order: index
        });
    });

    saveView({
        viewID: currentViewId,
        viewName: viewName,
        tableName: tableName,
        columnMappings: columnMappings
    });
}

// Auto-save functionality (optional)
let autoSaveTimeout;
function scheduleAutoSave() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
        if (currentViewId && validateForm()) {
            // Auto-save logic here if needed
            console.log('Auto-save triggered');
        }
    }, 30000); // Auto-save after 30 seconds of inactivity
}

// Trigger auto-save on form changes
$('#viewName, #tableName').on('input change', scheduleAutoSave);
$(document).on('input change', 'input[name="alias"], select[name="columnName"]', scheduleAutoSave);
