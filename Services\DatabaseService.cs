using System.Data;
using Microsoft.Data.SqlClient;
using Historical_Web.Models;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace Historical_Web.Services
{
    public class DatabaseService
    {
        private readonly string _connectionString;
        private readonly string _insertConnectionString;


        public DatabaseService(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ??
                throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

            _insertConnectionString = configuration.GetConnectionString("InsertConnection")
                ?? throw new InvalidOperationException("Insert connection not found.");
        }

            private async Task<List<string>> GetTableColumnsAsync(SqlConnection connection, string tableName)
        {
            var columns = new List<string>();

            string query = $@"
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = '{tableName}'";

            using (var command = new SqlCommand(query, connection))
            {
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        columns.Add(reader.GetString(0));
                    }
                }
            }

            return columns;
        }
        public async Task<List<string>> GetTableNamesAsync()
        {
            var tables = new List<string>();

            using (var connection = new SqlConnection(_connectionString))
            {
                await connection.OpenAsync();

                // Query to get user tables
                string query = @"
                    SELECT TABLE_NAME
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_TYPE = 'BASE TABLE'
                    AND TABLE_NAME LIKE 'Historical%'
                    ORDER BY TABLE_NAME";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            tables.Add(reader.GetString(0));
                        }
                    }
                }
            }

            return tables;
        }

        private Dictionary<string, Tuple<string, string, string, string>> GetLookupTables()
        {
            return new Dictionary<string, Tuple<string, string, string, string>>
            {
                // Format: { "MainColumn", new Tuple<string, string, string, string>("lookupTable", "lookupColumn", "alias", "displayColumn") }
                { "Currency", new Tuple<string, string, string, string>("Currncy", "Abbreviation", "T0", "Abbreviation") },
                { "DCIP", new Tuple<string, string, string, string>("ItemTypes", "Id", "T1", "Name") },
                { "Class", new Tuple<string, string, string, string>("Class", "Id", "T2", "Name") },
                { "ItemState", new Tuple<string, string, string, string>("FieldVals", "ValueX", "T3", "Name") },
                //{ "User_", new Tuple<string, string, string, string>("SysUser", "Id", "T4", "Name") },
                { "ActionCode_", new Tuple<string, string, string, string>("ExceptionFieldVals", "Id", "T5", "Lookupvalue") },
                { "ATM_Regions", new Tuple<string, string, string, string>("ATM_Region_LookUp", "Id", "T6", "Display") },
                { "ATM_TransactionType", new Tuple<string, string, string, string>("ATM_TraxType_LookUp", "Id", "T7", "Display") },
                { "ATM_PDCom_TRX", new Tuple<string, string, string, string>("ATM_PDCom_TRX_LookUp", "Id", "T8", "Display") },
                { "ATM_Group", new Tuple<string, string, string, string>("ATM_Group_LookUp", "Id", "T9", "Display") },
                { "DropDown_2", new Tuple<string, string, string, string>("DropDown_2_Lookup", "Id", "T10", "Display") },
                { "TransactionStatus", new Tuple<string, string, string, string>("TransactionStatus_Lookup", "Id", "T11", "Display") },
                { "TrxCategory", new Tuple<string, string, string, string>("ATM_TrxCategory_Lookup", "Id", "T12", "Display") },
                //{ "ReasonId", new Tuple<string, string, string, string>("vw_IMATCH_ResolutionCodes", "Id", "T13", "Description") },
                { "AccountId", new Tuple<string, string, string, string>("Account", "Id", "T14", "Name") },
                { "PoolId", new Tuple<string, string, string, string>("AccountPool", "Id", "T15", "Name") },
                { "CaseId", new Tuple<string, string, string, string>("Cases", "Id", "T16", "Name") },
                { "ActionField", new Tuple<string, string, string, string>("ActionField_Lookup", "Id", "T17", "Display") }
            };
        }

        private Dictionary<string, string> GetSpecialJoinConditions()
        {
            return new Dictionary<string, string>
            {
                { "ItemState", "AND T3.Id=195" }
            };
        }

        public async Task<DataTable> ExecuteQueryAsync(
            string tableName,
            List<QueryCondition> conditions,
            GridView? selectedView,
            int page,
            int pageSize,
            string sortColumn,
            string sortDirection)
        {
            var dataTable = new DataTable();
            var columnsToSkip = new HashSet<string>(StringComparer.OrdinalIgnoreCase) { "name2", "User_" };

            using (var connection = new SqlConnection(_connectionString))
            {
                await connection.OpenAsync();
                var sourceColumns = await GetTableColumnsAsync(connection, tableName);

                // Prepare column names
                var includedColumns = new List<string>();
                foreach (var col in sourceColumns)
                {
                    if (!columnsToSkip.Contains(col))
                        includedColumns.Add(col);
                }

                List<string> baseColumns;
                if (selectedView != null && !string.IsNullOrEmpty(selectedView.ColumnMappings))
                {
                    var mappings = JsonConvert.DeserializeObject<Dictionary<string, string>>(selectedView.ColumnMappings);
                    baseColumns = new List<string> { "id" };
                    baseColumns.AddRange(mappings?.Keys.ToList() ?? includedColumns);
                }
                else
                {
                    baseColumns = includedColumns;
                }

                var lookupTables = GetLookupTables();
                var specialJoinConditions = GetSpecialJoinConditions();

                var joinClauses = new List<string>();
                var expandedColumns = new List<string>();
                foreach (var col in baseColumns)
                {
                    if (lookupTables.TryGetValue(col, out var lookupInfo))
                    {
                        var (lookupTable, lookupColumn, lookupAlias, displayColumn) = lookupInfo;
                        var joinClause = $"LEFT JOIN [{lookupTable}] {lookupAlias} WITH(NOLOCK) ON [{tableName}].[{col}] = {lookupAlias}.[{lookupColumn}]";

                        if (specialJoinConditions.TryGetValue(col, out var condition))
                            joinClause += " " + condition;

                        joinClauses.Add(joinClause);
                        expandedColumns.Add($"[{lookupAlias}].[{displayColumn}] AS [{col}]");
                    }
                    else
                    {
                        expandedColumns.Add($"[{tableName}].[{col}] AS [{col}]");
                    }
                }

                // Build WHERE clause
                string whereClause = string.Empty;
                if (conditions.Count > 0)
                {
                    var conditionStrings = conditions.Select(c => c.ToSqlString());
                    whereClause = $"WHERE {string.Join(" AND ", conditionStrings)}";
                }

                int offset = (page - 1) * pageSize;

                // Initial query
                string query = $@"
                    SELECT * FROM [{tableName}] WITH(NOLOCK)
                    {whereClause}
                    ORDER BY {sortColumn} {sortDirection}
                    OFFSET {offset} ROWS
                    FETCH NEXT {pageSize} ROWS ONLY";

                // Inject SELECT columns
                query = query.Replace("SELECT *", "SELECT " + string.Join(", ", expandedColumns));

                // Inject JOINs
                if (joinClauses.Any())
                {
                    var fromPattern = $"FROM [{tableName}]";
                    var fromIndex = query.IndexOf(fromPattern, StringComparison.OrdinalIgnoreCase);

                    if (fromIndex > 0)
                    {
                        var whereIndex = query.IndexOf("WHERE", fromIndex, StringComparison.OrdinalIgnoreCase);

                        if (whereIndex > 0)
                        {
                            var beforeWhere = query.Substring(0, whereIndex).TrimEnd();
                            var afterWhere = query.Substring(whereIndex);
                            query = beforeWhere + " " + string.Join(" ", joinClauses) + " " + afterWhere;
                        }
                        else
                        {
                            var beforeFrom = query.Substring(0, fromIndex + fromPattern.Length);
                            var afterFrom = query.Substring(fromIndex + fromPattern.Length);
                            query = beforeFrom + " " + string.Join(" ", joinClauses) + afterFrom;
                        }
                    }
                }

                // Execute the query
                using (var command = new SqlCommand(query, connection))
                using (var adapter = new SqlDataAdapter(command))
                {
                    adapter.Fill(dataTable);
                }

                // Get total count for pagination
                string countQuery = $@"SELECT COUNT(*) FROM [{tableName}] WITH(NOLOCK) {whereClause}";
                using (var countCommand = new SqlCommand(countQuery, connection))
                {
                    var totalCount = await countCommand.ExecuteScalarAsync();
                    dataTable.ExtendedProperties["TotalRecords"] = totalCount;
                }
            }

            return dataTable;
        }
        public async Task<MoveToActiveResult> MoveToActiveAsync(string sourceTable, List<int> selectedIds, string currentUser)
        {
            if (selectedIds == null || selectedIds.Count == 0)
                return new MoveToActiveResult { RowsProcessed = 0, SkippedIds = new List<int>(), ProcessedIds = new List<int>() };

            // Check which IDs have already been moved from this specific source table
            var alreadyMovedIds = await CheckAlreadyMovedAsync(selectedIds, sourceTable);
            var idsToProcess = selectedIds.Except(alreadyMovedIds).ToList();

            if (idsToProcess.Count == 0)
            {
                return new MoveToActiveResult
                {
                    RowsProcessed = 0,
                    SkippedIds = alreadyMovedIds,
                    ProcessedIds = new List<int>(),
                    Message = "All selected rows have already been moved to active."
                };
            }

            int rowCount = 0;
            var originalIds = new List<long>();
            var newIds = new List<long>();
            string idList = string.Join(",", idsToProcess);

            using (var readConn = new SqlConnection(_connectionString))
            using (var writeConn = new SqlConnection(_insertConnectionString))
            {
                await readConn.OpenAsync();
                await writeConn.OpenAsync();

                var sourceDb = GetDatabaseName(readConn);
                var targetDb = GetDatabaseName(writeConn);
                string tempTableName = $"#TempItem_{Guid.NewGuid():N}";

                // 1. Get max ID from MoveToActiveLog
                long baseId;
                using (var readCmd = new SqlCommand { Connection = readConn, CommandTimeout = 180 })
                {
                    readCmd.CommandText = $@"
                        SELECT ISNULL(MAX(InsertedID), 1999999999)
                        FROM MoveToActiveLog";
                    baseId = Convert.ToInt64(await readCmd.ExecuteScalarAsync()) + 1;
                }

                using (var cmd = new SqlCommand { Connection = writeConn, CommandTimeout = 180 })
                {

                    // 2. Create temp table with same structure
                    cmd.CommandText = $@"
                        SELECT TOP 0 * INTO {tempTableName}
                        FROM [{targetDb}].[dbo].[Item] WITH(NOLOCK)";
                    await cmd.ExecuteNonQueryAsync();

                    // 3. Get common columns between source and target
                    cmd.CommandText = $@"
                        DECLARE @ColumnList NVARCHAR(MAX) = '';

                        SELECT @ColumnList = STRING_AGG(COLUMN_NAME, ', ')
                        FROM [{targetDb}].INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_NAME = 'Item'
                        AND COLUMN_NAME IN (
                            SELECT COLUMN_NAME
                            FROM [{sourceDb}].INFORMATION_SCHEMA.COLUMNS
                            WHERE TABLE_NAME = '{sourceTable}'
                        );

                        SELECT @ColumnList AS ColumnList;";
                    string commonColumns;
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        await reader.ReadAsync();
                        commonColumns = reader["ColumnList"]?.ToString() ?? string.Empty;
                    }

                    if (string.IsNullOrWhiteSpace(commonColumns))
                        throw new Exception("No matching columns found between source and target tables.");

                    // 4. Copy data from source to temp table
                    cmd.CommandText = $@"
                        INSERT INTO {tempTableName} ({commonColumns})
                        SELECT {commonColumns}
                        FROM [{sourceDb}].[dbo].[{sourceTable}] WITH(NOLOCK)
                        WHERE ID IN ({idList})";
                    rowCount = await cmd.ExecuteNonQueryAsync();

                    if (rowCount == 0)
                        return new MoveToActiveResult
                        {
                            RowsProcessed = 0,
                            SkippedIds = alreadyMovedIds,
                            ProcessedIds = idsToProcess,
                            Message = "No rows found to process."
                        };

                    // 5. Create mapping table
                    cmd.CommandText = $@"
                        CREATE TABLE #IdMapping (OldId BIGINT, NewId BIGINT);

                        INSERT INTO #IdMapping (OldId, NewId)
                        SELECT ID, {baseId} + ROW_NUMBER() OVER (ORDER BY ID) - 1
                        FROM {tempTableName};";
                    await cmd.ExecuteNonQueryAsync();

                    // 6. Capture original IDs
                    cmd.CommandText = "SELECT OldId FROM #IdMapping ORDER BY OldId;";
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                            originalIds.Add(reader.GetInt64(0));
                    }

                    // 7. Update temp table IDs
                    cmd.CommandText = $@"
                        UPDATE t
                        SET t.ID = m.NewId
                        FROM {tempTableName} t
                        INNER JOIN #IdMapping m ON t.ID = m.OldId;";
                    await cmd.ExecuteNonQueryAsync();

                    // 8. Capture new IDs
                    cmd.CommandText = "SELECT NewId FROM #IdMapping ORDER BY OldId;";
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                            newIds.Add(reader.GetInt64(0));
                    }

                    // 9. Insert into final table
                    cmd.CommandText = $@"
                        INSERT INTO [{targetDb}].[dbo].[Item]
                        SELECT * FROM {tempTableName};

                        DROP TABLE {tempTableName};
                        DROP TABLE #IdMapping;";
                    await cmd.ExecuteNonQueryAsync();
                }

                // 10. Log operation
                await LogMoveToActiveOperationAsync(readConn, originalIds, newIds, currentUser, sourceTable);
            }

            return new MoveToActiveResult
            {
                RowsProcessed = rowCount,
                SkippedIds = alreadyMovedIds,
                ProcessedIds = idsToProcess,
                Message = alreadyMovedIds.Count > 0
                    ? $"Processed {rowCount} rows. Skipped {alreadyMovedIds.Count} rows that were already moved."
                    : $"Successfully processed {rowCount} rows."
            };
        }

        private async Task<List<int>> CheckAlreadyMovedAsync(List<int> selectedIds, string sourceTable)
        {
            var alreadyMovedIds = new List<int>();

            if (selectedIds == null || selectedIds.Count == 0)
                return alreadyMovedIds;

            using (var connection = new SqlConnection(_connectionString))
            {
                await connection.OpenAsync();

                string idList = string.Join(",", selectedIds);
                string query = $@"
                    SELECT DISTINCT OriginalID
                    FROM MoveToActiveLOG
                    WHERE OriginalID IN ({idList}) AND SourceTableName = @sourceTable";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@sourceTable", sourceTable);
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            alreadyMovedIds.Add(Convert.ToInt32(reader["OriginalID"]));
                        }
                    }
                }
            }

            return alreadyMovedIds;
        }

        private async Task LogMoveToActiveOperationAsync(SqlConnection conn, List<long> originalIds, List<long> newIds, string userId, string sourceTableName)
        {
            using (var cmd = new SqlCommand())
            {
                cmd.Connection = conn;
                cmd.CommandText = @"
                    INSERT INTO MoveToActiveLog (OriginalID, InsertedID, UserID, SourceTableName)
                    VALUES (@OriginalID, @InsertedID, @UserID, @SourceTableName)";

                cmd.Parameters.Add("@OriginalID", SqlDbType.BigInt);
                cmd.Parameters.Add("@InsertedID", SqlDbType.BigInt);
                cmd.Parameters.Add("@UserID", SqlDbType.NVarChar, 100);
                cmd.Parameters.Add("@SourceTableName", SqlDbType.NVarChar, 128);

                for (int i = 0; i < originalIds.Count; i++)
                {
                    cmd.Parameters["@OriginalID"].Value = originalIds[i];
                    cmd.Parameters["@InsertedID"].Value = newIds[i];
                    cmd.Parameters["@UserID"].Value = userId;
                    cmd.Parameters["@SourceTableName"].Value = sourceTableName;

                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        private string GetDatabaseName(SqlConnection connection)
        {
            var builder = new SqlConnectionStringBuilder(connection.ConnectionString);

            if (!string.IsNullOrWhiteSpace(builder.InitialCatalog))
                return builder.InitialCatalog;

            throw new InvalidOperationException("Database name not found in the connection string.");
        }

        public async Task<object> GetRowDetailsAsync(int id, string sourceTable)
        {
            var tab1Results = new List<object>();
            var tab2Results = new List<object>();

            using (var conn = new SqlConnection(_connectionString))
            {
                await conn.OpenAsync();

                // 🔹 Tab 1: ItemAuditTrail
                tab1Results = await GetItemAuditTrailAsync(conn, id, sourceTable);

                // 🔹 Tab 2: MoveToActiveLOG
                tab2Results = await GetMoveToActiveLogAsync(conn, id, sourceTable);
            }

            return new
            {
                tab1 = tab1Results,
                tab2 = tab2Results
            };
        }


        private string GetAuditTrailTableName(string sourceTable)
        {
            if (string.IsNullOrWhiteSpace(sourceTable))
            {
                return "ItemAuditTrail";
            }

            if (!System.Text.RegularExpressions.Regex.IsMatch(sourceTable, @"^[a-zA-Z0-9_-]+$"))
            {
                return "ItemAuditTrail";
            }

            var yearMatch = System.Text.RegularExpressions.Regex.Match(sourceTable, @"(\d{4})");

            if (yearMatch.Success)
            {
                string year = yearMatch.Groups[1].Value;

                if (int.TryParse(year, out int yearInt) && yearInt >= 2000 && yearInt <= 2099)
                {
                    return $"ItemAuditTrail{year}";
                }
            }

            var alternateYearMatch = System.Text.RegularExpressions.Regex.Match(sourceTable, @"[_-](\d{4})");
            if (alternateYearMatch.Success)
            {
                string year = alternateYearMatch.Groups[1].Value;

                if (int.TryParse(year, out int yearInt) && yearInt >= 2000 && yearInt <= 2099)
                {
                    return $"ItemAuditTrail{year}";
                }
            }

            return "ItemAuditTrail";
        }

        private async Task<List<object>> GetItemAuditTrailAsync(SqlConnection conn, int id, string sourceTable)
        {
            var results = new List<object>();

            string auditTrailTableName = GetAuditTrailTableName(sourceTable);

            Console.WriteLine($"GetItemAuditTrailAsync: sourceTable='{sourceTable}' -> auditTrailTable='{auditTrailTableName}'");

            var query = $@"
                SELECT
                    su.Name AS UserName,
                    fd.FieldName,
                    ia.OldValue,
                    ia.NewValue,
                    ia.EffTime
                FROM [{auditTrailTableName}] ia
                LEFT JOIN FieldDesc fd ON fd.Id = ia.FieldId
                LEFT JOIN SysUser su ON su.Id = ia.UserId
                WHERE ia.ItemId = CASE
                    WHEN @id < 0 THEN ABS(@id)
                    ELSE @id
                END
                ORDER BY ia.EffTime DESC";

            using (var cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@id", id);
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            userName = reader["UserName"]?.ToString(),
                            fieldName = reader["FieldName"]?.ToString(),
                            oldValue = reader["OldValue"]?.ToString(),
                            newValue = reader["NewValue"]?.ToString(),
                            effTime = Convert.ToDateTime(reader["EffTime"]).ToString("yyyy-MM-dd HH:mm:ss")
                        });
                    }
                }
            }

            return results;
        }

        private async Task<List<object>> GetMoveToActiveLogAsync(SqlConnection conn, int id, string sourceTable)
        {
            var results = new List<object>();

            // Enhanced query to handle positive and negative @id on originalid
            // Search for both the original ID and its negative counterpart
            var query = @"
                SELECT OriginalID, InsertedID, InsertedAt, UserID, SourceTableName
                FROM MoveToActiveLOG
                WHERE SourceTableName = @sourceTable
                AND (OriginalID = @id OR OriginalID = -@id)";

            using (var cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@id", id);
                cmd.Parameters.AddWithValue("@sourceTable", sourceTable);
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            originalID = Convert.ToInt64(reader["OriginalID"]),
                            insertedID = Convert.ToInt64(reader["InsertedID"]),
                            insertedAt = Convert.ToDateTime(reader["InsertedAt"]).ToString("yyyy-MM-dd HH:mm:ss"),
                            userID = reader["UserID"]?.ToString(),
                            sourceTableName = reader["SourceTableName"]?.ToString()
                        });
                    }
                }
            }

            return results;
        }

        public async Task<List<Dictionary<string, string>>> GetDistinctColumnValuesAsync(string tableName, string columnName, List<QueryCondition> conditions)
        {
            var values = new List<Dictionary<string, string>>();
            using (var connection = new SqlConnection(_connectionString))
            {
                await connection.OpenAsync();
                var lookupTables = GetLookupTables();
                var specialJoinConditions = GetSpecialJoinConditions();
                var joinClauses = new List<string>();
                string selectValue, selectDisplay;
                if (lookupTables.TryGetValue(columnName, out var lookupInfo))
                {
                    var (lookupTable, lookupColumn, lookupAlias, displayColumn) = lookupInfo;
                    var joinClause = $"LEFT JOIN [{lookupTable}] {lookupAlias} WITH(NOLOCK) ON [{tableName}].[{columnName}] = {lookupAlias}.[{lookupColumn}]";
                    if (specialJoinConditions.TryGetValue(columnName, out var condition))
                        joinClause += " " + condition;
                    joinClauses.Add(joinClause);
                    selectValue = $"[{tableName}].[{columnName}]";
                    selectDisplay = $"[{lookupAlias}].[{displayColumn}]";
                }
                else
                {
                    selectValue = $"[{tableName}].[{columnName}]";
                    selectDisplay = selectValue;
                }
                string whereClause = string.Empty;
                if (conditions.Count > 0)
                {
                    var conditionStrings = conditions.Select(c => c.ToSqlString());
                    whereClause = $"WHERE {string.Join(" AND ", conditionStrings)}";
                }
                string query = $@"SELECT DISTINCT {selectValue} AS Value, {selectDisplay} AS Display FROM [{tableName}] WITH(NOLOCK) ";
                if (joinClauses.Any())
                {
                    query += string.Join(" ", joinClauses) + " ";
                }
                query += whereClause;
                using (var command = new SqlCommand(query, connection))
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        var value = reader["Value"]?.ToString();
                        var display = reader["Display"]?.ToString();
                        values.Add(new Dictionary<string, string> {
                            { "value", value ?? string.Empty },
                            { "display", display ?? string.Empty }
                        });
                    }
                }
            }
            return values;
        }

        public async Task<List<string>> GetTableColumnsAsync(string tableName)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                await connection.OpenAsync();
                return await GetTableColumnsAsync(connection, tableName);
            }
        }
    }
}